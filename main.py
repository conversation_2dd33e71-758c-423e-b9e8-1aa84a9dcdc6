from dotenv import load_dotenv
from fastapi import FastAPI, Request, UploadFile, File, Form, HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Optional
import jwt
from fastapi.middleware.cors import CORSMiddleware
from src.privateGPT_res import WorkflowParameters, search_local_information_stream
from fastapi.responses import StreamingResponse, JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
import shutil
import uuid
import subprocess
from datetime import datetime
from typing import List, Dict
import logging
import traceback
import configparser
import platform
import time
import sys
from src.document_analysis import analyze_documents_stream
from src.image_analysis import analyze_image_stream
from src.scene_analysis import analyze_scene_stream
from src.jiuan_api import chat_with_model, analyze_image as jiuan_api_analyze_image, emergency_knowledge_qa


# 添加语音识别模块
try:
    from src.voice_recognition import SpeechRecognizer
    ENABLE_SPEECH_RECOGNITION = True
except ImportError as e:
    ENABLE_SPEECH_RECOGNITION = False
    logging.warning(f"语音识别模块导入失败，语音识别功能已禁用: {str(e)}")


# 存储每个会话的输出状态的字典
stream_status: Dict[str, bool] = {}

# 添加API函数调用模块
try:
    from src.agent import function_calling_agent
    ENABLE_FUNCTION_CALLING = True
    # 设置日志记录
    logging.info("LangChain Agent函数调用模块已加载，支持自然语言接口调用")
except ImportError as e:
    ENABLE_FUNCTION_CALLING = False
    logging.warning(f"LangChain Agent函数调用模块导入失败，接口调用功能已禁用: {str(e)}")

# 添加sensitive_filter目录到系统路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "sensitive_filter"))
# 导入敏感词过滤模块
try:
    from sensitive_words import check_sensitive_words, get_response_for_sensitive_query, SensitivityLevel
    # 设置敏感词配置文件路径
    SENSITIVE_WORDS_CONFIG = os.path.join(os.path.dirname(os.path.abspath(__file__)), "sensitive_filter", "sensitive_words.json")
    ENABLE_SENSITIVE_FILTER = True
except ImportError:
    # 如果导入失败，禁用敏感词过滤
    ENABLE_SENSITIVE_FILTER = False
    logging.warning("敏感词过滤模块导入失败，敏感词过滤功能已禁用")

load_dotenv()
app = FastAPI()

# 导入认证模块
from src.app import AuthService, create_user_router
from src.app.auth import security, verify_token, hash_password, create_access_token

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

origins = [
    "*"
]

#跨域设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 存储会话历史的字典，键为会话ID
chat_histories = {}

# 配置日志
# 确保日志目录存在
log_dir = "log"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "rag_system.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("rag_system")

# 初始化数据库管理器
from src.db_manager import DBManager
import uuid

try:
    db_manager = DBManager()
    logger.info("数据库管理器初始化成功")

    # 添加用户认证路由
    user_router = create_user_router(db_manager)
    app.include_router(user_router)
    logger.info("用户认证路由添加成功")

except Exception as e:
    logger.error(f"数据库管理器初始化失败: {str(e)}")
    db_manager = None

# 创建依赖注入函数（在try块外部定义，确保始终可用）
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户信息"""
    if db_manager is None:
        raise HTTPException(status_code=500, detail="数据库连接失败")

    try:
        # 直接解析JWT令牌，不使用verify_token函数的依赖注入
        import jwt
        payload = jwt.decode(credentials.credentials, "your-secret-key-change-in-production", algorithms=["HS256"])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="无效的认证令牌")

        user = db_manager.get_user_by_username(username)
        if user is None:
            raise HTTPException(status_code=401, detail="用户不存在")
        if user['status'] != 'approved':
            raise HTTPException(status_code=401, detail="用户未通过审核")
        return user
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="无效的认证令牌")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"认证失败: {str(e)}")
        raise HTTPException(status_code=401, detail="认证失败")

def get_current_admin_user(current_user: dict = Depends(get_current_user)):
    """获取当前管理员用户"""
    if current_user['role'] != 'admin':
        raise HTTPException(status_code=403, detail="需要管理员权限")
    return current_user

# 日志配置已在上面完成

# 创建必要的目录
# 确保临时文件目录存在
temp_docs_dir = "Private_GPT/temp_documents"
if not os.path.exists(temp_docs_dir):
    os.makedirs(temp_docs_dir)
    logger.info(f"创建临时文件目录: {temp_docs_dir}")

# 加载配置文件
config = configparser.ConfigParser()
try:
    # 明确指定 UTF-8 编码
    config.read("config.ini", encoding='utf-8')
except Exception as e:
    logger.error(f"使用 UTF-8 编码加载配置文件失败: {str(e)}")

# 初始化语音识别器（在配置文件加载后）
speech_recognizer = None
if ENABLE_SPEECH_RECOGNITION:
    try:
        speech_recognizer = SpeechRecognizer(config)
        if speech_recognizer.is_enabled:
            logger.info("语音识别器初始化成功")
        else:
            logger.warning("语音识别器已禁用")
            speech_recognizer = None
    except Exception as e:
        logger.error(f"语音识别器初始化失败: {str(e)}")
        speech_recognizer = None
else:
    logger.warning("语音识别功能未启用")

@app.post("/run_workflow/")
async def run_workflow(request: Request):
    """运行工作流程"""
    try:
        # 获取会话ID，如果没有则使用默认值
        session_id = request.headers.get("session-id", "default")

        # 解析请求体
        data = await request.json()
        query_data = data.get("query", "")

        # 判断输入类型并获取查询内容
        if isinstance(query_data, list):
            query = query_data[-1] if query_data else ""
        else:
            query = str(query_data)

        # 记录查询
        logger.info(f"收到查询: {query}")

        # 检查敏感词
        if ENABLE_SENSITIVE_FILTER:
            sensitivity_level, found_words = check_sensitive_words(query, SENSITIVE_WORDS_CONFIG)
            if sensitivity_level != SensitivityLevel.NONE:
                # 如果检测到敏感词，返回相应的回复
                sensitive_response = get_response_for_sensitive_query(sensitivity_level)
                logger.warning(f"检测到敏感词 (级别: {sensitivity_level.name}): {found_words}")

                # 返回敏感词回复
                async def generate_sensitive_response():
                    yield sensitive_response

                return StreamingResponse(
                    generate_sensitive_response(),
                    media_type='text/event-stream'
                )

        # 获取向量数据库类型
        vector_db_type = "qdrant"  # 只使用qdrant

        # Qdrant 数据库检查
        qdrant_host = config.get("QDRANT", "HOST", fallback="localhost")
        qdrant_port = config.getint("QDRANT", "PORT", fallback=7541)

        # 尝试连接 Qdrant 服务器
        try:
            from qdrant_client import QdrantClient
            client = QdrantClient(host=qdrant_host, port=qdrant_port)
            # 简单的健康检查
            try:
                # 使用 get_collections 方法
                collections = client.get_collections()
                logger.info(f"成功连接到Qdrant服务器: {qdrant_host}:{qdrant_port}")
            except Exception as e:
                logger.warning(f"Qdrant服务器连接失败: {qdrant_host}:{qdrant_port}")
                return JSONResponse(
                    content={"answer": f"错误: 无法连接到Qdrant服务器: {qdrant_host}:{qdrant_port}", "sources": []}
                )
        except Exception as e:
            logger.error(f"Qdrant服务器连接异常: {str(e)}")
            return JSONResponse(
                content={"answer": f"错误: 连接Qdrant服务器时出错: {str(e)}", "sources": []}
            )

        async def generate_response():
            # 初始化输出状态
            stream_status[session_id] = True
            async for token in search_local_information_stream(query):
                # 检查是否需要停止输出
                if not stream_status.get(session_id, True):
                    logger.info(f"会话 {session_id} 的输出已被终止")
                    break
                yield token
            # 清理输出状态
            stream_status.pop(session_id, None)

        return StreamingResponse(
            generate_response(),
            media_type='text/event-stream'
        )
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理查询时发生异常:\n{error_trace}")
        # 简化返回给前端的错误信息
        return JSONResponse(
            content={"answer": f"处理查询时出错，请联系管理员查看日志", "sources": []}
        )

# 添加清除历史记录的端点
@app.post("/clear_history/")
async def clear_history(request: Request):
    session_id = request.headers.get("session-id", "default")
    if session_id in chat_histories:
        chat_histories[session_id] = []
    return {"status": "success", "message": "历史记录已清除"}

# 添加错误处理
@app.exception_handler(Exception)
async def generic_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"message": str(exc)}
    )

# 用户认证相关路由将通过模块化方式添加

# 登录API已移至用户路由模块

# 管理员API已移至用户路由模块

# 页面路由
@app.get("/login")
async def read_login():
    return FileResponse("static/login.html")

@app.get("/register")
async def read_register():
    return FileResponse("static/register.html")

@app.get("/admin/users")
async def read_admin_users():
    return FileResponse("static/admin_users.html")

# 添加根路由 - 重定向到登录页面
@app.get("/")
async def read_index():
    # 直接返回登录页面，让前端JavaScript处理登录状态检查
    return FileResponse("static/login.html")

# 主应用页面路由（需要登录后访问）
@app.get("/app")
async def read_app():
    return FileResponse("static/index.html")

# 添加停止输出的端点
@app.post("/stop_stream/")
async def stop_stream(request: Request):
    """停止当前会话的流式输出"""
    try:
        session_id = request.headers.get("session-id", "default")
        stream_status[session_id] = False
        logger.info(f"停止会话 {session_id} 的输出流")
        return {"status": "success", "message": "已停止输出"}
    except Exception as e:
        logger.error(f"停止输出流时发生错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "停止输出流时发生错误"}
        )

# 添加文档分析页面路由
@app.get("/document_analysis")
async def read_document_analysis():
    return FileResponse("static/document_upload_query.html")
    #return FileResponse("static/document_analysis.html")

# 添加图片分析页面路由
@app.get("/image_analysis")
async def read_image_analysis():
    return FileResponse("static/image_upload_query.html")

# 添加场景分析页面路由
@app.get("/scene_analysis")
async def read_scene_analysis():
    return FileResponse("static/scene_analysis.html")

# 添加久安大模型页面路由
@app.get("/jiuan_model")
async def read_jiuan_model():
    return FileResponse("static/jiuan_model.html")

# 添加管理员反馈页面路由
@app.get("/admin/feedback")
async def read_admin_feedback():
    return FileResponse("static/admin_feedback.html")

# 添加管理员文档列表页面路由
@app.get("/admin/document_list")
async def read_admin_document_list():
    return FileResponse("static/admin_document_list.html")

# 添加文档列表页面路由
@app.get("/document_list")
async def read_document_list():
    return FileResponse("static/document_list.html")

# 添加获取文档列表的API端点，临时功能
@app.get("/list_documents/")
async def list_documents():
    """获取source_documents目录中的所有文档文件夹及其内容"""
    try:
        source_dir = "Private_GPT/source_documents"
        if not os.path.exists(source_dir) or not os.path.isdir(source_dir):
            logger.warning(f"源文档目录不存在: {source_dir}")
            return {"folders": []}

        # 获取所有文件夹
        folders = []
        for item in os.listdir(source_dir):
            item_path = os.path.join(source_dir, item)
            # 只处理目录，跳过文件
            if os.path.isdir(item_path) and not item.startswith('.'):
                # 获取文件夹中的文件
                files = []
                for file in os.listdir(item_path):
                    file_path = os.path.join(item_path, file)
                    if os.path.isfile(file_path) and not file.startswith('.'):
                        files.append(file)

                # 获取文件夹创建时间
                try:
                    created_time = os.path.getctime(item_path)
                except:
                    created_time = 0

                folders.append({
                    "name": item,
                    "files": files,
                    "created_time": created_time
                })

        # 按创建时间降序排序，最新的在前面
        folders.sort(key=lambda x: x["created_time"], reverse=True)

        # 移除创建时间字段，不需要返回给前端
        for folder in folders:
            folder.pop("created_time", None)

        return {"folders": folders}
    except Exception as e:
        logger.error(f"获取文档列表时发生错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"获取文档列表失败: {str(e)}"}
        )

# 添加文件上传端点
@app.post("/upload_documents/")
async def upload_documents(request: Request, title: str = Form(...), department: str = Form(...), files: List[UploadFile] = File(...)):
    # 创建唯一的文件夹名称，基于时间戳和UUID
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    folder_id = str(uuid.uuid4())[:8]
    folder_name = f"{timestamp}_{folder_id}_{title}_{department}"

    logger.info(f"开始处理上传文件，标题: {title}, 部门: {department}, 文件数量: {len(files)}")

    # 创建目标文件夹
    upload_folder = os.path.join("Private_GPT/source_documents", folder_name)
    os.makedirs(upload_folder, exist_ok=True)
    logger.info(f"创建文件夹: {upload_folder}")

    # 保存所有上传的文件
    file_paths = []
    for file in files:
        file_path = os.path.join(upload_folder, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        file_paths.append(file_path)
        logger.info(f"保存文件: {file_path}")

    # 运行ingest脚本处理文件
    try:
        # 设置环境变量，只处理新上传的文件夹
        env = os.environ.copy()
        env["SOURCE_DIRECTORY"] = upload_folder

        # 尝试添加PyTorch库路径到环境变量
        try:
            import torch
            import sys

            # 获取当前Python解释器路径
            python_executable = sys.executable
            logger.info(f"当前Python解释器: {python_executable}")

            # 获取torch库路径
            torch_lib_path = os.path.dirname(torch.__file__) + "/lib"
            if os.path.exists(torch_lib_path):
                logger.info(f"添加PyTorch库路径到环境变量: {torch_lib_path}")
                env["PATH"] = torch_lib_path + os.pathsep + env.get("PATH", "")

                # 添加其他可能的依赖路径
                torch_bin_path = os.path.dirname(torch.__file__) + "/bin"
                if os.path.exists(torch_bin_path):
                    logger.info(f"添加PyTorch bin路径到环境变量: {torch_bin_path}")
                    env["PATH"] = torch_bin_path + os.pathsep + env["PATH"]

            # 直接使用当前Python解释器运行脚本
            cmd = [python_executable, "Private_GPT/ingest_600.py"]
            logger.info(f"使用当前Python解释器执行命令: {' '.join(cmd)}")
        except ImportError:
            logger.warning("无法导入PyTorch，跳过设置PyTorch库路径")
            cmd = ["python", "Private_GPT/ingest_600.py"]

        logger.info(f"开始运行ingest脚本，处理文件夹: {upload_folder}")

        # 打印完整的命令行，便于调试
        logger.info(f"执行命令: {' '.join(cmd)}, 环境变量: SOURCE_DIRECTORY={upload_folder}")

        # 添加超时机制，避免处理卡住
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            timeout=600  # 设置10分钟超时
        )

        # 记录脚本输出
        logger.info(f"Ingest脚本标准输出:\n{result.stdout}")
        if result.stderr:
            logger.error(f"Ingest脚本错误输出:\n{result.stderr}")

        # 检查返回码
        if result.returncode != 0:
            logger.error(f"Ingest脚本返回非零状态码: {result.returncode}")

            # 检查是否是PyTorch错误
            if "shm.dll" in result.stderr:
                logger.error("检测到PyTorch加载错误，这是一个已知问题")
                return {
                    "status": "error",
                    "message": "文档处理失败：系统缺少必要的PyTorch组件。请联系管理员安装正确版本的PyTorch。"
                }

            # 其他错误
            return {
                "status": "error",
                "message": f"处理文件时出错，请联系管理员查看日志"
            }

        # 返回成功消息和文件路径
        logger.info(f"文件处理成功: {len(files)} 个文件")
        return {
            "status": "success",
            "message": f"已上传 {len(files)} 个文件",
            "file_paths": file_paths,
            "folder": folder_name,
            "department": department
        }
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理文件时发生异常:\n{error_trace}")
        # 简化返回给前端的错误信息
        return {
            "status": "error",
            "message": f"处理文件时出错，请联系管理员查看日志"
        }

# 添加获取处理状态的端点
@app.get("/process_status/{folder_name}")
async def process_status(folder_name: str):
    """检查文档处理状态"""
    folder_path = os.path.join("Private_GPT/source_documents", folder_name)
    logger.info(f"检查文件夹状态: {folder_name}")

    if not os.path.exists(folder_path):
        logger.warning(f"文件夹不存在: {folder_path}")
        return {"status": "failed", "message": "文件夹不存在"}

    # 检查是否存在处理完成标记
    if os.path.exists(os.path.join(folder_path, '.processing_complete')):
        logger.info(f"文件夹 {folder_name} 处理完成")
        return {"status": "completed", "message": "文档处理完成"}

    # 检查是否存在处理失败标记
    if os.path.exists(os.path.join(folder_path, '.processing_failed')):
        # 读取失败原因
        try:
            with open(os.path.join(folder_path, '.processing_failed'), 'r') as f:
                error_message = f.read()
            logger.warning(f"文件夹 {folder_name} 处理失败: {error_message}")
            return {"status": "failed", "message": error_message}
        except:
            logger.warning(f"文件夹 {folder_name} 处理失败，但无法读取具体原因")
            return {"status": "failed", "message": "文档处理失败，但无法读取具体原因"}

    # 检查处理是否超时（超过5分钟）
    folder_creation_time = os.path.getctime(folder_path)
    current_time = time.time()
    if current_time - folder_creation_time > 300:  # 5分钟 = 300秒
        logger.warning(f"文件夹 {folder_name} 处理超时")
        # 创建处理失败标记
        with open(os.path.join(folder_path, '.processing_failed'), 'w') as f:
            f.write("处理超时，请重试")
        return {"status": "failed", "message": "处理超时，请重试"}

    # 如果都不存在，则表示正在处理中
    logger.info(f"文件夹 {folder_name} 正在处理中")
    return {"status": "processing", "message": "文件正在处理中"}

# 添加数据库相关API路由
@app.get("/get_sessions/")
async def get_sessions(current_user: dict = Depends(get_current_user)):
    """获取会话列表（根据用户权限）"""
    try:
        if db_manager is None:
            logger.error("数据库管理器未初始化")
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "数据库连接失败"}
            )

        # 根据用户角色获取会话
        is_admin = current_user['role'] == 'admin'
        user_id = current_user['user_id']

        sessions = db_manager.get_sessions(user_id=user_id, is_admin=is_admin)
        return sessions
    except Exception as e:
        logger.error(f"获取会话失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "获取会话失败"}
        )

@app.get("/get_session_messages/{session_id}")
async def get_session_messages(session_id: str):
    """获取特定会话的所有消息"""
    try:
        if db_manager is None:
            logger.error("数据库管理器未初始化")
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "数据库连接失败"}
            )

        messages = db_manager.get_session_messages(session_id)
        return messages
    except Exception as e:
        logger.error(f"获取会话消息失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "获取会话消息失败"}
        )

@app.post("/save_session/")
async def save_session(request: Request, current_user: dict = Depends(get_current_user)):
    """保存会话"""
    try:
        if db_manager is None:
            logger.error("数据库管理器未初始化")
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "数据库连接失败"}
            )

        data = await request.json()
        session_id = data.get("session_id", "")
        title = data.get("title", "")

        if not session_id or not title:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "会话ID和标题不能为空"}
            )

        success = db_manager.save_session(session_id, title, current_user['user_id'])

        if success:
            return {"status": "success"}
        else:
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "保存会话失败"}
            )
    except Exception as e:
        logger.error(f"保存会话失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "保存会话失败"}
        )

@app.post("/save_message/")
async def save_message(request: Request, current_user: dict = Depends(get_current_user)):
    """保存消息"""
    try:
        if db_manager is None:
            logger.error("数据库管理器未初始化")
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "数据库连接失败"}
            )

        data = await request.json()
        session_id = data.get("session_id", "")
        user_message = data.get("user_message", "")
        assistant_response = data.get("assistant_response", "")
        thinking_process = data.get("thinking_process", None)
        source_documents = data.get("source_documents", None)

        if not session_id or not user_message or not assistant_response:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "会话ID、用户消息和助手响应不能为空"}
            )

        # 生成消息ID
        message_id = f"msg_{uuid.uuid4()}"

        success = db_manager.save_message(
            message_id,
            session_id,
            user_message,
            assistant_response,
            thinking_process,
            source_documents,
            current_user['user_id']
        )

        if success:
            return {"status": "success", "message_id": message_id}
        else:
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "保存消息失败"}
            )
    except Exception as e:
        logger.error(f"保存消息失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "保存消息失败"}
        )

@app.delete("/delete_session/{session_id}")
async def delete_session(session_id: str):
    """删除会话"""
    try:
        if db_manager is None:
            logger.error("数据库管理器未初始化")
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "数据库连接失败"}
            )

        if not session_id:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "会话ID不能为空"}
            )

        success = db_manager.delete_session(session_id)

        if success:
            return {"status": "success"}
        else:
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "删除会话失败"}
            )
    except Exception as e:
        logger.error(f"删除会话失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "删除会话失败"}
        )

# 添加新的反馈端点
@app.post("/save_feedback/")
async def save_feedback(request: Request):
    """保存用户反馈"""
    try:
        if db_manager is None:
            logger.error("数据库管理器未初始化")
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "数据库连接失败"}
            )

        data = await request.json()
        message_id = data.get("message_id", "")
        rating = data.get("rating", 0)
        comment = data.get("comment", "")

        if not message_id:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "消息ID不能为空"}
            )

        # 生成反馈ID
        feedback_id = f"fb_{uuid.uuid4()}"

        success = db_manager.save_feedback(feedback_id, message_id, rating, comment)

        if success:
            return {"status": "success", "feedback_id": feedback_id}
        else:
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "保存反馈失败"}
            )
    except Exception as e:
        logger.error(f"保存反馈失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "保存反馈失败"}
        )

@app.get("/admin/permanent_feedback/")
async def get_permanent_feedback(request: Request, limit: int = 100, offset: int = 0, rating: Optional[int] = None, include_deleted: bool = False):
    """获取永久存储的反馈数据（管理员使用）"""
    try:
        if db_manager is None:
            logger.error("数据库管理器未初始化")
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "数据库连接失败"}
            )

        # 获取永久存储的反馈数据
        feedback_data = db_manager.get_permanent_feedback(limit, offset, rating, include_deleted)

        # 获取总数
        total_count = db_manager.get_permanent_feedback_count(rating, include_deleted)

        # 返回数据，格式化为前端需要的格式
        return {
            "total": total_count,
            "offset": offset,
            "limit": limit,
            "items": feedback_data
        }
    except Exception as e:
        logger.error(f"获取永久反馈数据失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "获取永久反馈数据失败"}
        )

@app.delete("/admin/permanent_feedback/{feedback_id}")
async def delete_permanent_feedback(feedback_id: str):
    """删除永久存储的反馈数据（管理员使用）"""
    try:
        if db_manager is None:
            logger.error("数据库管理器未初始化")
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": "数据库连接失败"}
            )

        # 标记反馈为已删除
        success = db_manager.mark_permanent_feedback_deleted(feedback_id)

        if success:
            return {"success": True, "message": "反馈已成功删除"}
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "反馈不存在或删除失败"}
            )
    except Exception as e:
        logger.error(f"删除永久反馈数据失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": "删除反馈失败"}
        )

@app.post("/analyze_documents/")
async def analyze_documents(request: Request):
    """从固定文件夹加载文档并进行分析"""
    try:
        # 获取会话ID，如果没有则使用默认值
        session_id = request.headers.get("session-id", "default")

        # 解析请求体
        data = await request.json()
        query_data = data.get("query", "")
        directory_path = data.get("directory_path", r"D:\LLM\RAG\RAG_demo6.5.7_askcase\Private_GPT\upload_files")  # 使用原始字符串防止转义问题
        file_list = data.get("file_list", None)  # 新增参数，可选的文件列表

        # 判断输入类型并获取查询内容
        if isinstance(query_data, list):
            query = query_data[-1] if query_data else ""
        else:
            query = str(query_data)

        # 记录查询
        logger.info(f"收到文档分析查询: {query}")
        logger.info(f"分析目录: {directory_path}")
        logger.info(f"指定文件列表: {file_list if file_list else '未指定，将分析所有文件'}")

        # 检查敏感词
        if ENABLE_SENSITIVE_FILTER:
            sensitivity_level, found_words = check_sensitive_words(query, SENSITIVE_WORDS_CONFIG)
            if sensitivity_level != SensitivityLevel.NONE:
                # 如果检测到敏感词，返回相应的回复
                sensitive_response = get_response_for_sensitive_query(sensitivity_level)
                logger.warning(f"检测到敏感词 (级别: {sensitivity_level.name}): {found_words}")

                # 返回敏感词回复
                async def generate_sensitive_response():
                    yield sensitive_response

                return StreamingResponse(
                    generate_sensitive_response(),
                    media_type='text/event-stream'
                )

        # 分析文档并生成回答 - 使用直接分析方法，不通过向量数据库
        async def generate_analysis_response():
            # 初始化输出状态
            stream_status[session_id] = True
            async for token in analyze_documents_stream(query, directory_path, file_list):
                # 检查是否需要停止输出
                if not stream_status.get(session_id, True):
                    logger.info(f"会话 {session_id} 的文档分析输出已被终止")
                    break
                yield token
            # 清理输出状态
            stream_status.pop(session_id, None)

        return StreamingResponse(
            generate_analysis_response(),
            media_type='text/event-stream'
        )
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理文档分析查询时发生异常:\n{error_trace}")
        # 简化返回给前端的错误信息
        return JSONResponse(
            content={"answer": f"处理查询时出错，请联系管理员查看日志", "sources": []}
        )

# 添加临时文件上传端点
@app.post("/temp_document_upload/")
async def temp_document_upload(files: List[UploadFile] = File(...)):
    """上传文档到临时目录用于文档分析，不进行向量化处理"""
    try:
        # 创建唯一的临时文件夹名称
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_id = str(uuid.uuid4())[:8]
        temp_folder_name = f"temp_{timestamp}_{folder_id}"

        logger.info(f"开始处理临时文件上传，文件数量: {len(files)}")

        # 创建临时目录
        temp_folder = os.path.join("Private_GPT/temp_documents", temp_folder_name)
        os.makedirs(temp_folder, exist_ok=True)
        logger.info(f"创建临时文件夹: {temp_folder}")

        # 保存所有上传的文件
        file_paths = []
        for file in files:
            file_path = os.path.join(temp_folder, file.filename)
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            file_paths.append(file_path)
            logger.info(f"保存临时文件: {file_path}")

        # 返回成功消息和临时目录路径
        return {
            "status": "success",
            "message": f"已上传 {len(files)} 个临时文件",
            "file_paths": file_paths,
            "temp_directory": temp_folder
        }
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理临时文件上传时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"处理临时文件上传时出错: {str(e)}"
            }
        )

# 添加管理员删除文件的API端点
@app.delete("/admin/delete_document/")
async def delete_document(request: Request):
    """删除指定文件夹中的文件，并从向量数据库中删除相关向量"""
    try:
        # 解析请求体
        data = await request.json()
        folder_name = data.get("folder_name", "")
        file_name = data.get("file_name", "")

        if not folder_name or not file_name:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "文件夹名称和文件名不能为空"}
            )

        # 构建文件路径
        file_path = os.path.join("Private_GPT/source_documents", folder_name, file_name)
        logger.info(f"尝试删除文件: {file_path}")

        # 检查文件是否存在
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "文件不存在"}
            )

        # 首先从向量数据库中删除相关向量
        try:
            # 连接到Qdrant
            qdrant_host = config.get("QDRANT", "HOST", fallback="localhost")
            qdrant_port = config.getint("QDRANT", "PORT", fallback=7541)
            collection_name = config.get("QDRANT", "COLLECTION_NAME_QUERY", fallback="documents")

            from qdrant_client import QdrantClient, models
            # 使用URL格式连接
            client = QdrantClient(url=f"http://{qdrant_host}:{qdrant_port}")

            # 使用文件夹名和文件名作为过滤条件删除向量
            # 创建过滤条件，使用models.Filter和models.FieldCondition
            filter_condition = models.Filter(
                must=[
                    models.FieldCondition(
                        key="folder_name",
                        match=models.MatchValue(value=folder_name)
                    ),
                    models.FieldCondition(
                        key="file_name",
                        match=models.MatchValue(value=file_name)
                    )
                ]
            )

            # 使用 scroll API 获取匹配的点
            scroll_result = client.scroll(
                collection_name=collection_name,
                filter=filter_condition,
                limit=1000,  # 每页最多1000个结果
                with_vectors=False,  # 不需要向量数据
                with_payload=False   # 不需要负载数据
            )

            # 提取点的ID
            point_ids = [point.id for point in scroll_result[0]]

            # 如果有下一页，继续获取
            offset = scroll_result[1]
            while offset:
                scroll_result = client.scroll(
                    collection_name=collection_name,
                    filter=filter_condition,
                    limit=1000,
                    offset=offset,
                    with_vectors=False,
                    with_payload=False
                )
                # 添加新的点ID
                point_ids.extend([point.id for point in scroll_result[0]])
                # 更新偏移量
                offset = scroll_result[1]

            logger.info(f"找到与文件 {file_name} 相关的向量点: {len(point_ids)} 个")

            if point_ids:
                # 分批处理删除
                batch_size = 100
                for i in range(0, len(point_ids), batch_size):
                    batch = point_ids[i:i+batch_size]
                    client.delete(
                        collection_name=collection_name,
                        points=batch
                    )
                    logger.info(f"已删除第 {i//batch_size + 1} 批向量，共 {len(batch)} 个点")

                logger.info(f"已从向量数据库中删除与文件 {file_name} 相关的向量，共 {len(point_ids)} 个点")
            else:
                logger.warning(f"未找到与文件 {file_name} 相关的向量")

        except Exception as e:
            logger.error(f"从向量数据库删除向量时出错: {str(e)}")
            # 向量删除失败，但我们仍然继续删除文件

        # 删除文件
        os.remove(file_path)
        logger.info(f"文件已删除: {file_path}")

        # 检查文件夹是否为空，如果为空则删除文件夹
        folder_path = os.path.join("Private_GPT/source_documents", folder_name)
        remaining_files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f)) and not f.startswith('.')]
        if not remaining_files:
            logger.info(f"文件夹为空，删除文件夹: {folder_path}")
            # 删除所有隐藏文件
            for hidden_file in os.listdir(folder_path):
                hidden_file_path = os.path.join(folder_path, hidden_file)
                if os.path.isfile(hidden_file_path):
                    os.remove(hidden_file_path)
            # 删除文件夹
            os.rmdir(folder_path)

        return {"success": True, "message": "文件已成功删除"}
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"删除文件时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"删除文件时出错: {str(e)}"}
        )

# 添加管理员删除文件夹的API端点
@app.delete("/admin/delete_folder/")
async def delete_folder(request: Request):
    """删除指定文件夹及其所有文件，并从向量数据库中删除相关向量"""
    try:
        # 解析请求体
        data = await request.json()
        folder_name = data.get("folder_name", "")

        if not folder_name:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "文件夹名称不能为空"}
            )

        # 构建文件夹路径
        folder_path = os.path.join("Private_GPT/source_documents", folder_name)
        logger.info(f"尝试删除文件夹: {folder_path}")

        # 检查文件夹是否存在
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            logger.warning(f"文件夹不存在: {folder_path}")
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "文件夹不存在"}
            )

        # 首先从向量数据库中删除相关向量
        try:
            # 连接到Qdrant
            qdrant_host = config.get("QDRANT", "HOST", fallback="localhost")
            qdrant_port = config.getint("QDRANT", "PORT", fallback=7541)
            collection_name = config.get("QDRANT", "COLLECTION_NAME_QUERY", fallback="documents")

            from qdrant_client import QdrantClient, models
            # 使用URL格式连接
            client = QdrantClient(url=f"http://{qdrant_host}:{qdrant_port}")

            # 使用文件夹名作为过滤条件删除向量
            # 创建过滤条件，使用models.Filter和models.FieldCondition
            filter_condition = models.Filter(
                must=[
                    models.FieldCondition(
                        key="folder_name",
                        match=models.MatchValue(value=folder_name)
                    )
                ]
            )

            # 使用 scroll API 获取匹配的点
            scroll_result = client.scroll(
                collection_name=collection_name,
                filter=filter_condition,
                limit=1000,  # 每页最多1000个结果
                with_vectors=False,  # 不需要向量数据
                with_payload=False   # 不需要负载数据
            )

            # 提取点的ID
            point_ids = [point.id for point in scroll_result[0]]

            # 如果有下一页，继续获取
            offset = scroll_result[1]
            while offset:
                scroll_result = client.scroll(
                    collection_name=collection_name,
                    filter=filter_condition,
                    limit=1000,
                    offset=offset,
                    with_vectors=False,
                    with_payload=False
                )
                # 添加新的点ID
                point_ids.extend([point.id for point in scroll_result[0]])
                # 更新偏移量
                offset = scroll_result[1]

            logger.info(f"找到与文件夹 {folder_name} 相关的向量点: {len(point_ids)} 个")

            if point_ids:
                # 分批处理删除
                batch_size = 100
                for i in range(0, len(point_ids), batch_size):
                    batch = point_ids[i:i+batch_size]
                    client.delete(
                        collection_name=collection_name,
                        points=batch
                    )
                    logger.info(f"已删除第 {i//batch_size + 1} 批向量，共 {len(batch)} 个点")

                logger.info(f"已从向量数据库中删除与文件夹 {folder_name} 相关的向量，共 {len(point_ids)} 个点")
            else:
                logger.warning(f"未找到与文件夹 {folder_name} 相关的向量")

        except Exception as e:
            logger.error(f"从向量数据库删除向量时出错: {str(e)}")
            # 向量删除失败，但我们仍然继续删除文件夹

        # 删除文件夹中的所有文件
        for file_name in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file_name)
            if os.path.isfile(file_path):
                os.remove(file_path)
                logger.info(f"已删除文件: {file_path}")

        # 删除文件夹
        os.rmdir(folder_path)
        logger.info(f"已删除文件夹: {folder_path}")

        return {"success": True, "message": "文件夹已成功删除"}
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"删除文件夹时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"删除文件夹时出错: {str(e)}"}
        )


# 添加图片分析端点
@app.post("/analyze_image/")
async def analyze_image(request: Request, image: UploadFile = File(...)):
    """分析上传的图片"""
    try:
        # 获取查询 - 从请求头或查询参数获取
        query_from_header = request.headers.get("x-query", "")
        query_from_param = request.query_params.get("query", "")
        query = query_from_param or query_from_header or "请描述这张图片"

        # 获取会话ID
        session_id = request.headers.get("session-id", "default")

        # 记录详细信息，帮助调试
        logger.info(f"图片分析接口被调用: session_id={session_id}, 查询='{query}'")
        logger.info(f"上传的图片: 文件名={image.filename}, 内容类型={image.content_type}")

        # 检查敏感词
        if ENABLE_SENSITIVE_FILTER:
            sensitivity_level, found_words = check_sensitive_words(query, SENSITIVE_WORDS_CONFIG)
            if sensitivity_level != SensitivityLevel.NONE:
                sensitive_response = get_response_for_sensitive_query(sensitivity_level)
                logger.warning(f"检测到敏感词 (级别: {sensitivity_level.name}): {found_words}")

                async def generate_sensitive_response():
                    yield sensitive_response

                return StreamingResponse(
                    generate_sensitive_response(),
                    media_type='text/event-stream'
                )

        # 保存上传的图片
        temp_dir = "temp_images"
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        # 生成唯一的文件名
        file_extension = os.path.splitext(image.filename)[1] if image.filename and '.' in image.filename else '.jpg'
        unique_filename = f"{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(temp_dir, unique_filename)

        # 保存文件
        try:
            # 重置文件指针，以防之前已读取
            await image.seek(0)

            content = await image.read()
            if not content:
                logger.error("上传的图片内容为空")
                return JSONResponse(
                    content={"answer": "上传的图片内容为空", "sources": []}
                )

            logger.info(f"读取到图片内容，大小: {len(content)} 字节")

            with open(file_path, "wb") as buffer:
                buffer.write(content)

            logger.info(f"图片已保存到: {file_path}")

            # 检查文件是否真的保存了
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                logger.info(f"成功保存图片文件，文件大小: {file_size} 字节")
                if file_size == 0:
                    logger.error("保存的图片文件大小为0")
                    return JSONResponse(
                        content={"answer": "保存的图片文件大小为0", "sources": []}
                    )
            else:
                logger.error("图片文件似乎没有成功保存")
                return JSONResponse(
                    content={"answer": "无法保存图片文件", "sources": []}
                )

        except Exception as e:
            logger.error(f"保存图片文件时出错: {str(e)}", exc_info=True)
            return JSONResponse(
                content={"answer": f"保存图片文件时出错: {str(e)}", "sources": []}
            )

        logger.info(f"准备调用图片分析接口 analyze_image_stream，查询: '{query}'")

        async def generate_analysis_response():
            try:
                logger.info("开始生成图片分析响应流")
                token_count = 0
                async for token in analyze_image_stream(query, file_path):
                    token_count += 1
                    if token_count % 50 == 0:  # 每50个token记录一次日志
                        logger.info(f"已生成 {token_count} 个token")
                    yield token
                logger.info(f"图片分析完成，总共生成了 {token_count} 个token")
            except Exception as e:
                logger.error(f"生成分析响应时出错: {str(e)}", exc_info=True)
                yield f"\n错误：生成分析响应时出错: {str(e)}"
            finally:
                # 清理临时文件
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.info(f"临时图片文件已删除: {file_path}")
                    else:
                        logger.warning(f"尝试删除不存在的临时文件: {file_path}")
                except Exception as e:
                    logger.error(f"删除临时文件时出错: {str(e)}")

        logger.info("返回StreamingResponse")
        return StreamingResponse(
            generate_analysis_response(),
            media_type='text/event-stream'
        )

    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理图片分析查询时发生异常:\n{error_trace}")
        return JSONResponse(
            content={"answer": f"处理图片分析查询时出错，请联系管理员查看日志", "sources": []}
        )

# 添加场景分析端点
@app.post("/analyze_scene/")
async def analyze_scene(request: Request, image: UploadFile = File(...), scene: str = Form(...)):
    """根据选定的场景分析上传的图片"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")

        # 记录详细信息，帮助调试
        logger.info(f"场景分析接口被调用: session_id={session_id}, 场景={scene}")
        logger.info(f"上传的图片: 文件名={image.filename}, 内容类型={image.content_type}")

        # 检查敏感词
        if ENABLE_SENSITIVE_FILTER:
            sensitivity_level, found_words = check_sensitive_words(scene, SENSITIVE_WORDS_CONFIG)
            if sensitivity_level != SensitivityLevel.NONE:
                sensitive_response = get_response_for_sensitive_query(sensitivity_level)
                logger.warning(f"检测到敏感词 (级别: {sensitivity_level.name}): {found_words}")

                async def generate_sensitive_response():
                    yield sensitive_response

                return StreamingResponse(
                    generate_sensitive_response(),
                    media_type='text/event-stream'
                )

        # 保存上传的图片
        temp_dir = "temp_images"
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        # 生成唯一的文件名
        file_extension = os.path.splitext(image.filename)[1] if image.filename and '.' in image.filename else '.jpg'
        unique_filename = f"{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(temp_dir, unique_filename)

        # 保存文件
        try:
            # 重置文件指针，以防之前已读取
            await image.seek(0)

            content = await image.read()
            if not content:
                logger.error("上传的图片内容为空")
                return JSONResponse(
                    content={"answer": "上传的图片内容为空", "sources": []}
                )

            logger.info(f"读取到图片内容，大小: {len(content)} 字节")

            with open(file_path, "wb") as buffer:
                buffer.write(content)

            logger.info(f"图片已保存到: {file_path}")

            # 检查文件是否真的保存了
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                logger.info(f"成功保存图片文件，文件大小: {file_size} 字节")
                if file_size == 0:
                    logger.error("保存的图片文件大小为0")
                    return JSONResponse(
                        content={"answer": "保存的图片文件大小为0", "sources": []}
                    )
            else:
                logger.error("图片文件似乎没有成功保存")
                return JSONResponse(
                    content={"answer": "无法保存图片文件", "sources": []}
                )

        except Exception as e:
            logger.error(f"保存图片文件时出错: {str(e)}", exc_info=True)
            return JSONResponse(
                content={"answer": f"保存图片文件时出错: {str(e)}", "sources": []}
            )

        logger.info(f"准备调用场景分析接口 analyze_scene_stream，场景: '{scene}'")

        async def generate_scene_analysis_response():
            try:
                logger.info("开始生成场景分析响应流")
                token_count = 0
                async for token in analyze_scene_stream(scene, file_path):
                    token_count += 1
                    if token_count % 50 == 0:  # 每50个token记录一次日志
                        logger.info(f"已生成 {token_count} 个token")
                    yield token
                logger.info(f"场景分析完成，总共生成了 {token_count} 个token")
            except Exception as e:
                logger.error(f"生成场景分析响应时出错: {str(e)}", exc_info=True)
                yield f"\n错误：生成场景分析响应时出错: {str(e)}"
            finally:
                # 清理临时文件
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.info(f"临时图片文件已删除: {file_path}")
                    else:
                        logger.warning(f"尝试删除不存在的临时文件: {file_path}")
                except Exception as e:
                    logger.error(f"删除临时文件时出错: {str(e)}")

        logger.info("返回StreamingResponse")
        return StreamingResponse(
            generate_scene_analysis_response(),
            media_type='text/event-stream'
        )

    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理场景分析查询时发生异常:\n{error_trace}")
        return JSONResponse(
            content={"answer": f"处理场景分析查询时出错，请联系管理员查看日志", "sources": []}
        )

# 添加久安大模型聊天接口
@app.post("/jiuan_chat/")
async def jiuan_chat(request: Request):
    """与久安大模型对话"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        logger.info(f"久安大模型聊天接口被调用: session_id={session_id}")

        # 解析请求体
        data = await request.json()
        query_data = data.get("query", "")

        # 判断输入类型并获取查询内容
        if isinstance(query_data, list):
            query = query_data[-1] if query_data else ""
        else:
            query = str(query_data)

        if not query:
            return JSONResponse(
                content={"status": "error", "message": "查询内容不能为空"}
            )

        logger.info(f"用户查询: {query}")

        # 构造久安大模型API所需的chat_data格式
        chat_data = {
            "object": {
                "id": data.get("id", "chat_session_001"),
                "model": data.get("model", config.get("JIUAN_API", "CHAT_DEFAULT_MODEL", fallback="deepseek-r1-full")),
                "messages": [{"role": "user", "content": query}]
            }
        }

        # 调用久安大模型接口
        response = await chat_with_model(chat_data, config, logger)

        # 返回结果
        return JSONResponse(content=response)

    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理久安大模型聊天请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )



# 添加久安大模型图片分析接口
@app.post("/jiuan_analyze_image/")
async def jiuan_analyze_image(
    request: Request,
    image: UploadFile = File(...),
    query: Optional[str] = Form(None),
    history: Optional[str] = Form(None)
):
    """使用久安大模型分析图片内容"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")

        # 如果没有提供query，设置默认值
        if not query:
            query = "请描述这张图片"

        # 处理history参数
        history_list = []
        if history:
            try:
                # 尝试解析JSON格式的history
                import json
                history_list = json.loads(history)
                if not isinstance(history_list, list):
                    history_list = []
            except (json.JSONDecodeError, ValueError):
                logger.warning(f"无法解析history参数: {history}")
                history_list = []

        logger.info(f"久安大模型图片分析接口被调用: session_id={session_id}, query={query}")
        logger.info(f"上传的图片: 文件名={image.filename}, 内容类型={image.content_type}")
        logger.info(f"对话历史记录: {len(history_list)}条")

        # 保存临时文件
        temp_dir = "temp_files"
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        file_extension = os.path.splitext(image.filename)[1] if image.filename and '.' in image.filename else '.jpg'
        unique_filename = f"{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(temp_dir, unique_filename)

        # 读取并保存图片内容
        await image.seek(0)
        content = await image.read()
        if not content:
            logger.error("上传的图片内容为空")
            return JSONResponse(
                content={"status": "error", "message": "上传的图片内容为空"}
            )

        logger.info(f"读取到图片内容，大小: {len(content)} 字节")

        with open(file_path, "wb") as buffer:
            buffer.write(content)

        logger.info(f"图片已保存到: {file_path}")

        # 调用久安大模型图片分析接口
        response = await jiuan_api_analyze_image(file_path, query, config, logger, history_list)

        # 清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"临时图片文件已删除: {file_path}")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {str(e)}")

        # 返回结果
        return JSONResponse(content=response)

    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理久安大模型图片分析请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )

# 添加久安大模型视频分析接口（使用WebSocket）
@app.post("/jiuan_analyze_video/")
async def jiuan_analyze_video(
    request: Request,
    video: UploadFile = File(...),
    query: Optional[str] = Form(None),
    history: Optional[str] = Form(None)
):
    """使用久安大模型分析视频内容（基于WebSocket）"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")

        # 如果没有提供query，设置默认值
        if not query:
            query = "请分析这段视频"

        logger.info(f"久安大模型WebSocket视频分析接口被调用: session_id={session_id}, query={query}")
        logger.info(f"上传的视频: 文件名={video.filename}, 内容类型={video.content_type}")

        # 检查文件类型
        # if not video.content_type or not video.content_type.startswith('video/'):
        #     return JSONResponse(
        #         content={"status": "error", "message": "请上传有效的视频文件"}
        #     )

        # 保存临时文件
        temp_dir = "temp_files"
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        file_extension = os.path.splitext(video.filename)[1] if video.filename and '.' in video.filename else '.mp4'
        unique_filename = f"websocket_video_{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(temp_dir, unique_filename)

        # 读取并保存视频内容
        await video.seek(0)
        content = await video.read()
        if not content:
            logger.error("上传的视频内容为空")
            return JSONResponse(
                content={"status": "error", "message": "上传的视频内容为空"}
            )

        # 检查文件大小（10MB = 10 * 1024 * 1024 字节）
        max_file_size = 10 * 1024 * 1024  # 10MB
        if len(content) > max_file_size:
            logger.warning(f"视频文件过大: {len(content)} 字节 (限制: {max_file_size} 字节)")
            return JSONResponse(
                content={"status": "error", "message": f"视频文件大小不能超过10MB，当前文件大小: {len(content) / (1024*1024):.1f}MB"}
            )

        logger.info(f"读取到视频内容，大小: {len(content)} 字节 ({len(content) / (1024*1024):.2f}MB)")

        with open(file_path, "wb") as buffer:
            buffer.write(content)

        logger.info(f"视频已保存到: {file_path}")

        # 调用新的WebSocket视频分析接口
        from src.jiuan_api import get_websocket_video_analysis
        response = await get_websocket_video_analysis(file_path, query, config, logger)

        # 清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"临时视频文件已删除: {file_path}")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {str(e)}")

        # 返回结果
        return JSONResponse(content=response)

    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理久安大模型WebSocket视频分析请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )

# 添加久安大模型应急行业知识问答接口
@app.post("/jiuan_emergency_knowledge/")
async def jiuan_emergency_knowledge(request: Request):
    """久安大模型应急行业知识问答"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        logger.info(f"久安大模型应急知识问答接口被调用: session_id={session_id}")

        # 解析请求体
        data = await request.json()
        user_message = data.get("message", "")

        if not user_message:
            return JSONResponse(
                content={"status": "error", "message": "消息不能为空"}
            )

        # 调用久安大模型应急知识问答接口
        response = await emergency_knowledge_qa(user_message, config, logger)

        # 返回结果
        return JSONResponse(content=response)

    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理久安大模型应急知识问答请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )


# 添加语音对话页面路由
@app.get("/voice_chat")
async def read_voice_chat():
    """返回语音对话页面"""
    return FileResponse("static/voice_chat.html")

# 添加语音转文字接口
@app.post("/speech_to_text/")
async def speech_to_text(
    request: Request,
    audio: UploadFile = File(...),
    language: Optional[str] = Form("zh")
):
    """语音转文字接口"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        
        logger.info(f"语音识别接口被调用: session_id={session_id}")
        logger.info(f"上传的音频: 文件名={audio.filename}, 内容类型={audio.content_type}")
        
        if not speech_recognizer:
            return JSONResponse(
                content={"status": "error", "message": "语音识别服务未启用"}
            )
        
        # 验证文件格式
        if audio.filename:
            file_extension = audio.filename.split('.')[-1].lower()
            supported_formats = speech_recognizer.get_supported_formats()
            if file_extension not in supported_formats:
                logger.error(f"不支持的音频格式: {file_extension}")
                return JSONResponse(
                    content={
                        "status": "error", 
                        "message": f"不支持的音频格式。支持格式：{', '.join(supported_formats).upper()}"
                    }
                )
        
        # 检查文件大小
        max_size_mb = speech_recognizer.get_max_file_size_mb()
        # 读取文件内容来检查大小
        content = await audio.read()
        file_size = len(content)
        # 重置文件指针到开头
        await audio.seek(0)
        
        if file_size > max_size_mb * 1024 * 1024:
            return JSONResponse(
                content={
                    "status": "error", 
                    "message": f"文件大小超过{max_size_mb}MB限制"
                }
            )
        
        # 保存临时文件
        temp_dir = "temp_files"
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        file_extension = os.path.splitext(audio.filename)[1] if audio.filename and '.' in audio.filename else '.wav'
        unique_filename = f"{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(temp_dir, unique_filename)
        
        # 使用已读取的内容保存文件
        if not content:
            return JSONResponse(
                content={"status": "error", "message": "上传的音频内容为空"}
            )
        
        with open(file_path, "wb") as buffer:
            buffer.write(content)
        
        logger.info(f"音频已保存到: {file_path}")
        
        # 进行语音识别
        recognition_result = speech_recognizer.recognize_audio(file_path, language)
        
        # 清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"临时音频文件已删除: {file_path}")
        except Exception as e:
            logger.error(f"删除临时文件时出错: {str(e)}")
        
        return JSONResponse(content=recognition_result)
        
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理语音识别请求时发生异常:\n{error_trace}")
        return JSONResponse(
            content={"status": "error", "message": f"处理语音识别请求时出错: {str(e)}"}
        )



if __name__ == "__main__":
    uvicorn.run(app, host="***********", port=8089)
#运行命令：uvicorn main:app --host 0.0.0.0 --port 8089 --reload
#多进程部署：gunicorn main:app -k uvicorn.workers.UvicornWorker --bind ***********:8089 --workers 4  --timeout 60 --log-level info
#运行命令：uvicorn main:app --host 0.0.0.0 --port 8087 --reload