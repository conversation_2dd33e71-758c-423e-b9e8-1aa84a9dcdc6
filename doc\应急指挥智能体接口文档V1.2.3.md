# 应急指挥智能体接口文档

## 1 知识库问答模型

### 1.1 知识库问答接口

#### 接口描述

基于RAG（检索增强生成）技术的知识库问答接口，可根据用户问题查询相关文档，并结合大语言模型生成回答。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/run_workflow/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名 | 数据类型   | 是否必须 | 备注         |
| ----- | ----- | ------ | ---- | ---------- |
| 用户查询  | query | String | Y    | 用户的问题或查询内容 |

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                   |
| ----- | ---------- | ------ | ---- | -------------------- |
| 会话ID  | session-id | String | N    | 用于维护会话上下文，如不提供则使用默认值 |

#### 入参示例

```json
{
  "query": "消防应急指挥系统的主要功能有哪些？"
}
```

#### 返回参数

返回格式为流式响应（text/event-stream），包含以下内容：

1. 思考过程 - 使用`<think>...</think>`标签包裹
2. 文档来源 - 使用`<source>...</source>`标签包裹
3. 问题回答 - 直接文本内容

#### 返回示例

```
<think>正在分析问题并搜索相关文档...</think>
<source>> Private_GPT/source_documents/20250317_142206_75613293_111/消防应急指挥系统功能说明.pdf:
消防应急指挥系统是集成了多种信息技术的综合性平台，用于协助消防指挥员进行火灾扑救和应急救援的决策与指挥。</source>
消防应急指挥系统的主要功能包括：

1. 实时监控：通过视频监控、物联网传感器等设备实时监控火灾现场情况。
2. 资源调度：对消防车辆、人员、装备等资源进行智能调度和管理。
3. 通信指挥：提供多种通信手段，确保指挥中心与前线人员的信息畅通。
4. 态势分析：分析火灾发展趋势、蔓延路径，辅助指挥决策。
5. 预案管理：储存和调用各类应急预案，指导现场处置工作。
6. 数据可视化：将复杂数据转化为直观的图形界面，提高指挥效率。

这些功能相互协同，形成一个完整的指挥体系，有效提升消防救援的科学性和高效性。
```

### 1.2 上传文档接口

#### 接口描述

上传文档到知识库，支持多种文档格式如PDF、Word、TXT等。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/upload_documents/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名 | 数据类型   | 是否必须 | 备注                                          |
| ----- | ----- | ------ | ---- | ------------------------------------------- |
| 知识标题  | title | String | Y    | 上传文档的标题                                     |
| 文件    | files | File   | Y    | 支持的文件类型: .pdf, .csv, .txt, .md, .doc, .docx |

#### 返回参数

| 返回值显示名 | 返回值物理名 | 返回值类型  | 示例                           |
| ------ | ------ | ------ | ---------------------------- |
| 返回码    | code   | String | 0                            |
| 返回信息   | msg    | String | success                      |
| 文件夹ID  | folder | String | 20250317_142206_75613293_111 |

#### 返回示例

```json
{
  "status": "success",
  "message": "已上传 3 个文件",
  "file_paths": [
    "Private_GPT/source_documents/20250317_142206_75613293_111/消防应急预案.pdf",
    "Private_GPT/source_documents/20250317_142206_75613293_111/高层建筑灭火战术.docx",
    "Private_GPT/source_documents/20250317_142206_75613293_111/危险化学品处置规程.txt"
  ],
  "folder": "20250317_142206_75613293_111"
}
```

### 1.3 处理状态查询接口

#### 接口描述

查询文档处理的状态和进度。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/process_status/{folder_name}

#### 请求方法

GET

#### 请求参数

| 参数显示名 | 参数物理名       | 数据类型   | 是否必须 | 备注                      |
| ----- | ----------- | ------ | ---- | ----------------------- |
| 文件夹ID | folder_name | String | Y    | 上传文档时返回的文件夹名称，作为URL路径参数 |

#### 返回参数

| 返回值显示名 | 返回值物理名  | 返回值类型  | 示例        |
| ------ | ------- | ------ | --------- |
| 处理状态   | status  | String | completed |
| 状态信息   | message | String | 文档处理完成    |

#### 返回示例

```json
{
  "status": "completed",
  "message": "文档处理完成"
}
```

### 1.4 清除历史记录接口

#### 接口描述

清除特定会话的对话历史记录。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/clear_history/

#### 请求方法

POST

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                    |
| ----- | ---------- | ------ | ---- | --------------------- |
| 会话ID  | session-id | String | N    | 要清除历史的会话ID，如不提供则使用默认值 |

#### 返回参数

| 返回值显示名 | 返回值物理名  | 返回值类型  | 示例      |
| ------ | ------- | ------ | ------- |
| 状态     | status  | String | success |
| 消息     | message | String | 历史记录已清除 |

#### 返回示例

```json
{
  "status": "success",
  "message": "历史记录已清除"
}
```

### 1.5 事件文档分析接口

#### 接口描述

直接分析指定目录下的文档内容，无需预先建立向量数据库索引，支持实时分析文档并回答问题。支持选择性分析指定文件或分析目录下所有文件。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/analyze_documents/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名          | 数据类型          | 是否必须 | 备注                         |
| ----- | -------------- | ------------- | ---- | -------------------------- |
| 用户查询  | query          | String        | Y    | 用户的问题或查询内容                 |
| 文档目录  | directory_path | String        | N    | 要分析的文档所在目录路径，默认为系统配置的目录    |
| 文件列表  | file_list      | Array[String] | N    | 要分析的具体文件名列表，如不提供则分析目录下所有文件 |

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                   |
| ----- | ---------- | ------ | ---- | -------------------- |
| 会话ID  | session-id | String | N    | 用于维护会话上下文，如不提供则使用默认值 |

#### 入参示例

```json
{
  "query": "这份消防预案的主要内容是什么？",
  "directory_path": "D:\\Documents\\FireSafety",
  "file_list": ["高层建筑消防预案.pdf", "化工厂消防应急处置方案.docx"]
}
```

或者不指定具体文件：

```json
{
  "query": "消防指挥中心应该如何处置化学品泄漏火灾？",
  "directory_path": "D:\\Documents\\FireSafety"
}
```

#### 返回参数

返回格式为流式响应（text/event-stream），包含以下内容：

1. 思考过程 - 使用`<think>...</think>`标签包裹
2. 分析结果 - 直接文本内容

#### 返回示例

```
<think>正在分析问题并加载文档内容...</think>
<think>成功加载了2个文档片段，总计15000个字符。

正在准备分析...</think>
根据文档内容分析，消防指挥中心处置化学品泄漏火灾应采取以下步骤：

1. 信息收集：迅速确认泄漏化学品的种类、危险特性和泄漏量。
2. 警戒隔离：根据化学品危险性设立警戒区，疏散无关人员。
3. 专业力量：调集专业危化品处置队伍和装备。
4. 防护措施：确保救援人员穿戴适当的防护装备。
5. 控制泄漏：采用适当方法控制泄漏源。
6. 火灾扑救：选择合适的灭火剂，避免与化学品发生反应。
7. 环境监测：持续监测空气、水源等环境指标。

文档特别强调，不同类型的化学品需要采用不同的处置方法，指挥员必须根据专业知识和预案指导进行决策。
```

#### 支持的文档格式

- PDF文件 (.pdf)
- Word文档 (.docx)
- 文本文件 (.txt)
- CSV文件 (.csv)
- Markdown文件 (.md)

#### 注意事项

1. 文档目录路径需要有正确的读取权限
2. 建议控制单次分析的文档总量，避免内容过多影响分析效果
3. 如果目录下有不支持的文件格式会自动跳过

### 1.6 停止流式输出接口

#### 接口描述

停止当前正在进行的流式输出响应。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/stop_stream/

#### 请求方法

POST

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                     |
| ----- | ---------- | ------ | ---- | ---------------------- |
| 会话 ID | session-id | String | N    | 要停止输出的会话 ID，如不提供则使用默认值 |

#### 返回参数

| 返回值显示名 | 返回值物理名  | 返回值类型  | 示例      |
| ------ | ------- | ------ | ------- |
| 状态     | status  | String | success |
| 消息     | message | String | 已停止输出   |

#### 返回示例

```json
{
  "status": "success",
  "message": "已停止输出"
}
```

### 1.7 获取文档列表接口

#### 接口描述

获取知识库中已上传的所有文档文件夹及其内容。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/list_documents/

#### 请求方法

GET

#### 返回参数

| 返回值显示名 | 返回值物理名  | 返回值类型 | 示例         |
| ------ | ------- | ----- | ---------- |
| 文件夹列表  | folders | Array | 包含文件夹信息的数组 |

每个文件夹对象包含：

| 返回值显示名 | 返回值物理名 | 返回值类型         | 示例                                 |
| ------ | ------ | ------------- | ---------------------------------- |
| 文件夹名称  | name   | String        | 20250317_142206_75613293_111       |
| 文件列表   | files  | Array[String] | ["高层建筑火灾扑救预案.pdf", "化学品处置手册.docx"] |

#### 返回示例

```json
{
  "folders": [
    {
      "name": "20250317_142206_75613293_111",
      "files": [
        "高层建筑火灾扑救预案.pdf",
        "化学品处置手册.docx",
        "消防指挥中心应急预案.txt"
      ]
    },
    {
      "name": "20250316_153022_98765432_222",
      "files": [
        "森林火灾救援指南.pdf",
        "地震救援应急预案.docx"
      ]
    }
  ]
}
```

### 1.8 上传临时文档接口

#### 接口描述

上传文档到临时目录用于文档分析，不进行向量化处理。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/temp_document_upload/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名 | 数据类型 | 是否必须 | 备注                                          |
| ----- | ----- | ---- | ---- | ------------------------------------------- |
| 文件    | files | File | Y    | 支持的文件类型: .pdf, .csv, .txt, .md, .doc, .docx |

#### 返回参数

| 返回值显示名 | 返回值物理名      | 返回值类型         | 示例                                            |
| ------ | ----------- | ------------- | --------------------------------------------- |
| 状态     | status      | String        | success                                       |
| 消息     | message     | String        | 文件上传成功                                        |
| 文件路径   | file_paths  | Array[String] | ["/path/to/file1.pdf", "/path/to/file2.docx"] |
| 临时文件夹  | temp_folder | String        | temp_20250418_123045_abcd1234                 |

#### 返回示例

```json
{
  "status": "success",
  "message": "已上传 2 个文件到临时目录",
  "file_paths": [
    "Private_GPT/temp_documents/temp_20250418_123045_abcd1234/化工厂火灾应急预案.pdf",
    "Private_GPT/temp_documents/temp_20250418_123045_abcd1234/高层建筑消防设计规范.docx"
  ],
  "temp_folder": "temp_20250418_123045_abcd1234"
}
```

### 1.9 图片分析接口

#### 接口描述

分析上传的图片内容，使用多模态大语言模型生成对图片的描述或回答相关问题。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/analyze_image/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名 | 数据类型 | 是否必须 | 备注                                      |
| ----- | ----- | ---- | ---- | --------------------------------------- |
| 图片文件  | image | File | Y    | 支持的图片格式: .jpg, .jpeg, .png, .gif, .webp |

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                        |
| ----- | ---------- | ------ | ---- | ------------------------- |
| 会话ID  | session-id | String | N    | 用于维护会话上下文，如不提供则使用默认值      |
| 查询内容  | x-query    | String | N    | 关于图片的问题，如不提供则默认为"请描述这张图片" |

#### 查询参数

| 参数显示名 | 参数物理名 | 数据类型   | 是否必须 | 备注                        |
| ----- | ----- | ------ | ---- | ------------------------- |
| 查询内容  | query | String | N    | 关于图片的问题，如不提供则默认为"请描述这张图片" |

#### 返回参数

返回格式为流式响应（text/event-stream），包含以下内容：

1. 思考过程 - 使用`<think>...</think>`标签包裹
2. 分析结果 - 直接文本内容

#### 返回示例

```
<think>正在分析上传的图片...</think>
<think>图片加载成功，尺寸为1024x768，正在进行内容识别...</think>
这张图片展示了一个消防演习现场。图中可以看到：

1. 多名身穿消防服的消防员正在操作消防设备
2. 现场有一辆红色消防车，车上装备了高压水枪
3. 背景是一栋多层建筑，似乎是演习的目标建筑
4. 地面上铺设了消防水带，连接到消防栓

这应该是一次消防部门组织的灭火演练，目的是提高消防员的实战能力和协同作战水平。
```

### 1.10 场景分析接口

#### 接口描述

根据选定的场景（防汛抗旱、安全生产、消防巡查）分析上传的图片，生成针对性的安全隐患分析报告。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/analyze_scene/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名 | 数据类型   | 是否必须 | 备注                                                    |
| ----- | ----- | ------ | ---- | ----------------------------------------------------- |
| 图片文件  | image | File   | Y    | 支持的图片格式: .jpg, .jpeg, .png, .gif, .webp               |
| 场景类型  | scene | String | Y    | 场景类型，可选值: "flood"(防汛抗旱), "safety"(安全生产), "fire"(消防巡查) |

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                   |
| ----- | ---------- | ------ | ---- | -------------------- |
| 会话ID  | session-id | String | N    | 用于维护会话上下文，如不提供则使用默认值 |

#### 返回参数

返回格式为流式响应（text/event-stream），包含以下内容：

1. 思考过程 - 使用`<think>...</think>`标签包裹
2. 分析结果 - 直接文本内容，包含以下几个方面的分析：
   - 场景解析
   - 潜在风险或安全隐患
   - 重大事故隐患提示
   - 法律依据（具体条目）
   - 整改建议

#### 返回示例

```
<think>正在分析图片并根据选定场景进行评估...</think>
<think>使用ollama类型的语言模型: llava

正在初始化模型...

场景类型: fire</think>

【场景解析】
图片显示的是一个建筑物内的消防通道，通道内堆放了杂物和行李箱，严重阻碍了消防通道的畅通。消防通道的墙上贴有明显的消防通道标识，但实际通道被占用。

【潜在风险或安全隐患】
1. 消防通道被堵塞，影响人员疏散和消防救援
2. 通道内堆放的物品可能成为火灾蔓延的媒介
3. 紧急情况下可能导致人员拥堵，增加伤亡风险
4. 消防设备可能无法及时到达火灾现场

【重大事故隐患提示】
该情况属于重大消防安全隐患，一旦发生火灾，将严重影响人员疏散和消防救援，可能导致群死群伤事故。

【法律依据】
1. 《中华人民共和国消防法》第二十八条：任何单位、个人不得占用、堵塞、封闭疏散通道、安全出口、消防车通道。
2. 《建筑设计防火规范》(GB 50016-2014)第6.4.1条：疏散走道应保持畅通，禁止堆放物品。
3. 《消防安全责任制实施办法》第十五条：单位应当保障疏散通道、安全出口畅通。

【整改建议】
1. 立即清理消防通道内的所有杂物，确保通道完全畅通
2. 在通道醒目位置增设"禁止堆放物品"警示标识
3. 加强日常巡查和管理，建立专人负责制
4. 对相关责任人进行消防安全教育培训
5. 制定消防通道管理制度，明确责任和惩罚措施
```

## 2 会话管理与用户反馈

### 2.1 获取会话列表接口

#### 接口描述

获取所有历史会话的列表。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/get_sessions/

#### 请求方法

GET

#### 返回参数

| 返回值显示名 | 返回值物理名     | 返回值类型  | 示例                         |
| ------ | ---------- | ------ | -------------------------- |
| 会话ID   | session_id | String | sess_123456789             |
| 会话标题   | title      | String | 关于水利工程的讨论                  |
| 创建时间   | created_at | String | 2025-04-17T10:30:45.123456 |
| 更新时间   | updated_at | String | 2025-04-17T11:15:22.654321 |

#### 返回示例

```json
[
  {
    "session_id": "sess_123456789",
    "title": "高层建筑火灾扑救战术讨论",
    "created_at": "2025-04-17T10:30:45.123456",
    "updated_at": "2025-04-17T11:15:22.654321"
  },
  {
    "session_id": "sess_987654321",
    "title": "危险化学品事故处置预案查询",
    "created_at": "2025-04-16T14:22:33.123456",
    "updated_at": "2025-04-16T15:45:12.654321"
  }
]
```

### 2.2 获取会话消息接口

#### 接口描述

获取特定会话的所有消息记录。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/get_session_messages/{session_id}

#### 请求方法

GET

#### 请求参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注        |
| ----- | ---------- | ------ | ---- | --------- |
| 会话ID  | session_id | String | Y    | 作为URL路径参数 |

#### 返回参数

| 返回值显示名 | 返回值物理名             | 返回值类型  | 示例                         |
| ------ | ------------------ | ------ | -------------------------- |
| 消息ID   | message_id         | String | msg_123456789              |
| 会话ID   | session_id         | String | sess_123456789             |
| 用户消息   | user_message       | String | 水利工程的主要类型有哪些？              |
| 助手回复   | assistant_response | String | 水利工程主要分为以下几种类型：...         |
| 思考过程   | thinking_process   | String | <think>正在分析问题...</think>   |
| 引用文档   | source_documents   | String | <source>文档引用内容...</source> |
| 创建时间   | created_at         | String | 2025-04-17T10:30:45.123456 |

#### 返回示例

```json
[
  {
    "message_id": "msg_123456789",
    "session_id": "sess_123456789",
    "user_message": "高层建筑火灾扑救的主要战术有哪些？",
    "assistant_response": "高层建筑火灾扑救主要采用以下战术：\n1. 内部进攻：消防员进入建筑内部直接灭火\n2. 外部防御：利用高喷车、云梯车等进行外部灭火\n3. 垂直分割：利用防火分区阻止火势蔓延\n4. 水平切割：在火势蔓延路径上设置水幕阻断\n5. 排烟降温：利用正压送风或机械排烟降低温度\n6. 疏散救人：优先疏散被困人员，特别是火场上方楼层",
    "thinking_process": "<think>正在分析问题并搜索相关文档...</think>",
    "source_documents": "<source>高层建筑火灾扑救战术主要包括内部进攻与外部防御相结合，垂直分割与水平切割相配合，以及排烟降温和疏散救人等战术方法。</source>",
    "created_at": "2025-04-17T10:30:45.123456"
  },
  {
    "message_id": "msg_987654321",
    "session_id": "sess_123456789",
    "user_message": "这些战术在实施时有什么注意事项？",
    "assistant_response": "高层建筑火灾扑救战术实施注意事项：\n1. 内部进攻：消防员必须配备完整防护装备和足够空气呼吸器\n2. 外部防御：注意高空风力影响，调整水流方向和强度\n3. 垂直分割：确保防火分区完整性，检查管道井等薄弱环节\n4. 水平切割：水幕设置位置应考虑建筑结构和火势蔓延方向\n5. 排烟降温：避免不当排烟造成的烟气倒灌和火势蔓延\n6. 疏散救人：建立专门的搜救小组，优先搜救重点区域",
    "thinking_process": "<think>这是一个跟进问题，需要继续分析高层建筑火灾扑救战术的注意事项...</think>",
    "source_documents": null,
    "created_at": "2025-04-17T10:35:22.123456"
  }
]
```

### 2.3 保存会话接口

#### 接口描述

保存或更新会话信息。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/save_session/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注        |
| ----- | ---------- | ------ | ---- | --------- |
| 会话ID  | session_id | String | Y    | 唯一标识会话的ID |
| 会话标题  | title      | String | Y    | 会话的标题或主题  |

#### 入参示例

```json
{
  "session_id": "sess_123456789",
  "title": "高层建筑火灾扑救战术讨论"
}
```

#### 返回参数

| 返回值显示名 | 返回值物理名 | 返回值类型  | 示例      |
| ------ | ------ | ------ | ------- |
| 状态     | status | String | success |

#### 返回示例

```json
{
  "status": "success"
}
```

### 2.4 保存消息接口

#### 接口描述

保存用户消息和AI回复到数据库。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/save_message/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名              | 数据类型   | 是否必须 | 备注                       |
| ----- | ------------------ | ------ | ---- | ------------------------ |
| 会话ID  | session_id         | String | Y    | 消息所属的会话ID                |
| 用户消息  | user_message       | String | Y    | 用户发送的消息内容                |
| 助手回复  | assistant_response | String | Y    | AI助手的回复内容                |
| 思考过程  | thinking_process   | String | N    | AI的思考过程，通常包含在<think>标签中  |
| 引用文档  | source_documents   | String | N    | 引用的文档内容，通常包含在<source>标签中 |

#### 入参示例

```json
{
  "session_id": "sess_123456789",
  "user_message": "高层建筑火灾扑救的主要战术有哪些？",
  "assistant_response": "高层建筑火灾扑救主要采用以下战术：\n1. 内部进攻：消防员进入建筑内部直接灭火\n2. 外部防御：利用高喷车、云梯车等进行外部灭火\n3. 垂直分割：利用防火分区阻止火势蔓延\n4. 水平切割：在火势蔓延路径上设置水幕阻断\n5. 排烟降温：利用正压送风或机械排烟降低温度\n6. 疏散救人：优先疏散被困人员，特别是火场上方楼层",
  "thinking_process": "<think>正在分析问题并搜索相关文档...</think>",
  "source_documents": "<source>高层建筑火灾扑救战术主要包括内部进攻与外部防御相结合，垂直分割与水平切割相配合，以及排烟降温和疏散救人等战术方法。</source>"
}
```

#### 返回参数

| 返回值显示名 | 返回值物理名     | 返回值类型  | 示例            |
| ------ | ---------- | ------ | ------------- |
| 状态     | status     | String | success       |
| 消息ID   | message_id | String | msg_123456789 |

#### 返回示例

```json
{
  "status": "success",
  "message_id": "msg_123456789"
}
```

### 2.5 删除会话接口

#### 接口描述

删除指定的会话及其所有消息。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/delete_session/{session_id}

#### 请求方法

DELETE

#### 请求参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注        |
| ----- | ---------- | ------ | ---- | --------- |
| 会话ID  | session_id | String | Y    | 作为URL路径参数 |

#### 返回参数

| 返回值显示名 | 返回值物理名 | 返回值类型  | 示例      |
| ------ | ------ | ------ | ------- |
| 状态     | status | String | success |

#### 返回示例

```json
{
  "status": "success"
}
```

### 2.6 保存反馈接口

#### 接口描述

保存用户对AI回复的反馈评价。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/save_feedback/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名      | 数据类型    | 是否必须 | 备注                 |
| ----- | ---------- | ------- | ---- | ------------------ |
| 消息ID  | message_id | String  | Y    | 要评价的消息ID           |
| 评分    | rating     | Integer | Y    | 评分：0表示负面评价，1表示正面评价 |
| 评论    | comment    | String  | N    | 用户的详细反馈意见          |

#### 入参示例

```json
{
  "message_id": "msg_123456789",
  "rating": 1,
  "comment": "回答详细介绍了高层建筑灭火战术，对我们消防指挥工作很有帮助！"
}
```

#### 返回参数

| 返回值显示名 | 返回值物理名      | 返回值类型  | 示例           |
| ------ | ----------- | ------ | ------------ |
| 状态     | status      | String | success      |
| 反馈ID   | feedback_id | String | fb_123456789 |

#### 返回示例

```json
{
  "status": "success",
  "feedback_id": "fb_123456789"
}
```

### 2.7 获取永久反馈接口

#### 接口描述

获取永久存储的用户反馈数据（管理员使用）。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/admin/permanent_feedback/

#### 请求方法

GET

#### 请求参数

| 参数显示名 | 参数物理名           | 数据类型    | 是否必须 | 备注                  |
| ----- | --------------- | ------- | ---- | ------------------- |
| 每页数量  | limit           | Integer | N    | 每页返回的反馈数量，默认为100    |
| 偏移量   | offset          | Integer | N    | 分页偏移量，默认为0          |
| 评分筛选  | rating          | Integer | N    | 按评分筛选（0为负面，1为正面）    |
| 包含已删除 | include_deleted | Boolean | N    | 是否包含已删除的反馈，默认为false |

#### 返回参数

| 返回值显示名 | 返回值物理名 | 返回值类型   | 示例     |
| ------ | ------ | ------- | ------ |
| 总数     | total  | Integer | 150    |
| 偏移量    | offset | Integer | 0      |
| 每页数量   | limit  | Integer | 100    |
| 反馈数据   | items  | Array   | 反馈数据数组 |

每个反馈数据对象包含：

| 返回值显示名 | 返回值物理名             | 返回值类型   | 示例                             |
| ------ | ------------------ | ------- | ------------------------------ |
| 反馈ID   | feedback_id        | String  | fb_123456789                   |
| 消息ID   | message_id         | String  | msg_123456789                  |
| 会话ID   | session_id         | String  | sess_123456789                 |
| 用户消息   | user_message       | String  | 高层建筑火灾扑救的主要战术有哪些？              |
| 助手回复   | assistant_response | String  | 高层建筑火灾扑救主要采用以下战术：...           |
| 评分     | rating             | Integer | 1                              |
| 评论     | comment            | String  | 回答详细介绍了高层建筑灭火战术，对我们消防指挥工作很有帮助！ |
| 创建时间   | created_at         | String  | 2025-04-17T10:40:15.123456     |
| 是否已删除  | is_deleted         | Boolean | false                          |

#### 返回示例

```json
{
  "total": 150,
  "offset": 0,
  "limit": 100,
  "items": [
    {
      "feedback_id": "fb_123456789",
      "message_id": "msg_123456789",
      "session_id": "sess_123456789",
      "user_message": "高层建筑火灾扑救的主要战术有哪些？",
      "assistant_response": "高层建筑火灾扑救主要采用以下战术：\n1. 内部进攻：消防员进入建筑内部直接灭火\n2. 外部防御：利用高喷车、云梯车等进行外部灭火\n3. 垂直分割：利用防火分区阻止火势蔓延\n4. 水平切割：在火势蔓延路径上设置水幕阻断\n5. 排烟降温：利用正压送风或机械排烟降低温度\n6. 疏散救人：优先疏散被困人员，特别是火场上方楼层",
      "rating": 1,
      "comment": "回答详细介绍了高层建筑灭火战术，对我们消防指挥工作很有帮助！",
      "created_at": "2025-04-17T10:40:15.123456",
      "is_deleted": false
    },
    {
      "feedback_id": "fb_987654321",
      "message_id": "msg_987654321",
      "session_id": "sess_987654321",
      "user_message": "化学品泄漏事故如何处置？",
      "assistant_response": "化学品泄漏事故处置步骤如下：\n1. 迅速确认泄漏化学品的种类和危险特性\n2. 设立警戒区，疏散无关人员\n3. 穿戴适当的防护装备\n4. 采用适当方法控制泄漏源\n5. 防止泄漏物质扩散和二次污染",
      "rating": 0,
      "comment": "回答缺少对不同类型化学品的具体处置方法说明，不够实用。",
      "created_at": "2025-04-16T15:30:22.123456",
      "is_deleted": false
    }
  ]
}
```

### 2.8 删除永久反馈接口

#### 接口描述

删除永久存储的反馈数据（管理员使用）。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/admin/permanent_feedback/{feedback_id}

#### 请求方法

DELETE

#### 请求参数

| 参数显示名 | 参数物理名       | 数据类型   | 是否必须 | 备注        |
| ----- | ----------- | ------ | ---- | --------- |
| 反馈ID  | feedback_id | String | Y    | 作为URL路径参数 |

#### 返回参数

| 返回值显示名 | 返回值物理名  | 返回值类型   | 示例      |
| ------ | ------- | ------- | ------- |
| 成功标志   | success | Boolean | true    |
| 消息     | message | String  | 反馈已成功删除 |

#### 返回示例

```json
{
  "success": true,
  "message": "反馈已成功删除"
}
```

## 3 久安大模型接口

### 3.1 久安大模型对话接口

#### 接口描述

与久安大模型进行对话。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/jiuan_chat/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名 | 数据类型   | 是否必须 | 备注         |
| ----- | ----- | ------ | ---- | ---------- |
| 用户查询  | query | String | Y    | 用户的问题或查询内容 |

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                   |
| ----- | ---------- | ------ | ---- | -------------------- |
| 会话ID  | session-id | String | N    | 用于维护会话上下文，如不提供则使用默认值 |

#### 入参示例

```json
{
  "query": "何时启动应急防汛一级响应"
}
```

#### 返回参数（非流式输出）

| 返回值显示名 | 返回值物理名          | 返回值类型  | 示例                                   |
| ------ | --------------- | ------ | ------------------------------------ |
| 请求ID   | request_id      | String | ac2687aa-634a-406c-ba8b-17144e7ddeac |
| 响应时间   | date            | String | 2025-05-28T14:29:11Z                 |
| 回答内容   | answer          | String | 关于何时启动应急防汛一级响应...                    |
| 会话ID   | conversation_id | String | 91ce102e-b9a0-4615-a371-e3333b3b7669 |
| 消息ID   | message_id      | String | 08d6bec1-a5e1-445a-91d0-e052123a2d86 |
| 完成标志   | is_completion   | String | null                                 |
| 处理内容   | content         | Array  | 包含详细处理流程的数组                          |

content数组中每个元素包含：

| 返回值显示名 | 返回值物理名        | 返回值类型   | 示例           |
| ------ | ------------- | ------- | ------------ |
| 结果类型   | result_type   | String  | empty_result |
| 事件代码   | event_code    | Integer | 0            |
| 事件消息   | event_message | String  | ""           |
| 事件类型   | event_type    | String  | RAGAgent     |
| 事件ID   | event_id      | String  | "2"          |
| 事件状态   | event_status  | String  | done         |
| 内容类型   | content_type  | String  | rag          |
| 可见范围   | visible_scope | String  | all          |
| 输出内容   | outputs       | Object  | 包含具体的输出数据    |

#### 返回示例

**成功响应示例：**

```json
{
    "status": "success",
    "data": {
      "request_id": "ac2687aa-634a-406c-ba8b-17144e7ddeac",
      "date": "2025-05-28T14:29:11Z",
      "answer": "关于何时启动应急防汛一级响应，这通常取决于具体的防汛应急预案和当地的实际情况。一般来说，当面临严重的洪水威胁，且预计将对人民生命财产安全造成重大损失时，相关部门会评估并决定是否启动应急防汛一级响应。然而，具体的启动时机和条件可能因地区、防汛预案以及当时的天气、水文等因素而有所不同。因此，无法给出确切的启动时间。\n\n为了获取更准确的信息，建议查阅当地的防汛应急预案或联系相关部门进行咨询。同时，也请密切关注当地的气象和水文信息，以便及时了解洪水威胁并采取相应的防范措施。",
      "conversation_id": "91ce102e-b9a0-4615-a371-e3333b3b7669",
      "message_id": "08d6bec1-a5e1-445a-91d0-e052123a2d86",
      "is_completion": null,
      "content": [
        {
          "result_type": "",
          "event_code": 0,
          "event_message": "",
          "event_type": "function_call",
          "event_id": "0",
          "event_status": "done",
          "content_type": "function_call",
          "visible_scope": "",
          "outputs": {
            "text": {
              "arguments": {
                "origin_query": "何时启动应急防汛一级响应"
              },
              "component_code": "RAGAgent",
              "component_name": "知识问答"
            }
          }
        },
        {
          "result_type": "",
          "event_code": 0,
          "event_message": "",
          "event_type": "RAGAgent",
          "event_id": "1",
          "event_status": "preparing",
          "content_type": "status",
          "visible_scope": "",
          "outputs": {}
        },
        {
          "result_type": "empty_result",
          "event_code": 0,
          "event_message": "",
          "event_type": "RAGAgent",
          "event_id": "2",
          "event_status": "done",
          "content_type": "rag",
          "visible_scope": "all",
          "outputs": {
            "name_cn": "大模型回答",
            "references": [],
            "text": ""
          }
        },
        {
          "result_type": "empty_result",
          "event_code": 0,
          "event_message": "",
          "event_type": "RAGAgent",
          "event_id": "2",
          "event_status": "done",
          "content_type": "rag",
          "visible_scope": "all",
          "outputs": {
            "name_cn": "大模型回答",
            "text": "关于何时启动应急防汛一级响应，这通常取决于具体的防汛应急预案和当地的实际情况。一般来说，当面临严重的洪水威胁，且预计将对人民生命财产安全造成重大损失时，相关部门会评估并决定是否启动应急防汛一级响应。然而，具体的启动时机和条件可能因地区、防汛预案以及当时的天气、水文等因素而有所不同。因此，无法给出确切的启动时间。\n\n为了获取更准确的信息，建议查阅当地的防汛应急预案或联系相关部门进行咨询。同时，也请密切关注当地的气象和水文信息，以便及时了解洪水威胁并采取相应的防范措施。",
            "references": []
          },
          "usage": {
            "prompt_tokens": 936,
            "completion_tokens": 216,
            "total_tokens": 1152,
            "name": "ERNIE-3.5-8K-0701",
            "type": "chat"
          }
        },
        {
          "result_type": "",
          "event_code": 0,
          "event_message": "",
          "event_type": "RAGAgent",
          "event_id": "3",
          "event_status": "success",
          "content_type": "status",
          "visible_scope": "",
          "outputs": {}
        }
      ]
    }
}
```

**错误响应示例：**

```json
{
  "status": "error",
  "message": "查询内容不能为空"
}
```

### 3.2 久安大模型图片分析接口

#### 接口描述

使用久安大模型分析上传图片的内容，支持提供分析问题和对话历史记录。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/jiuan_analyze_image/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名   | 数据类型   | 是否必须 | 备注                                      |
| ----- | ------- | ------ | ---- | --------------------------------------- |
| 图片文件  | image   | File   | Y    | 支持的图片格式: .jpg, .jpeg, .png, .gif, .webp |
| 查询内容  | query   | String | N    | 关于图片的问题，如不提供则默认为"请描述这张图片"               |
| 历史记录  | history | String | N    | JSON格式的对话历史记录列表                         |

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                   |
| ----- | ---------- | ------ | ---- | -------------------- |
| 会话ID  | session-id | String | N    | 用于维护会话上下文，如不提供则使用默认值 |

#### history参数格式

history参数应为JSON字符串，包含对话历史记录数组：

```json
[
  {
    "role": "user",
    "content": "这是什么建筑？"
  },
  {
    "role": "assistant", 
    "content": "这是一座现代化的办公楼。"
  }
]
```

#### 返回参数（非流式输出）

| 返回值显示名 | 返回值物理名  | 返回值类型  | 示例                       |
| ------ | ------- | ------ | ------------------------ |
| 状态     | status  | String | success                  |
| 数据     | data    | Object | 包含久安大模型API的原始响应数据        |
| 错误消息   | message | String | 仅在status为error时返回，描述错误信息 |

久安大模型API的响应数据格式：

| 返回值显示名 | 返回值物理名  | 返回值类型   | 示例                  |
| ------ | ------- | ------- | ------------------- |
| 分析结果   | result  | Object  | 包含msg等字段的分析结果       |
| 历史记录   | history | Array   | 更新后的对话历史记录列表        |
| 状态码    | status  | Integer | 200                 |
| 时间戳    | time    | String  | 2025-05-28 09:06:56 |

#### 返回示例

**成功响应示例：**

```json
{
  "status": "success",
  "data": {
    "result": {
      "msg": "这是一辆红色的越野皮卡车，车身上有中文标识，表明它是一辆应急救援车。车顶装有警示灯，车身侧面有救援相关的标识和徽章。车辆设计坚固，适合在复杂地形中行驶，用于紧急救援任务。",
      "img": "",
      "boxList": []
    },
    "history": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "请分析这张图片"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "http://10.204.161.180:9000/afdmx/bigsure_img/202505/28/d95da74549ba4a65aa519c1ea7faa2eb.jpg"
            }
          }
        ]
      },
      {
        "role": "assistant",
        "content": "这是一辆红色的越野皮卡车，车身上有中文标识，表明它是一辆应急救援车。车顶装有警示灯，车身侧面有救援相关的标识和徽章。车辆设计坚固，适合在复杂地形中行驶，用于紧急救援任务。"
      }
    ],
    "status": 200,
    "time": "2025-05-28 09:06:56"
  }
}
```

**错误响应示例：**

```json
{
  "status": "error",
  "message": "上传的图片内容为空"
}
```

#### 支持的图片格式

- JPEG文件 (.jpg, .jpeg)
- PNG文件 (.png)
- GIF文件 (.gif)  
- WebP文件 (.webp)

#### 注意事项

1. 图片文件大小不能超过10MB
2. history参数必须是有效的JSON格式
3. 如果不提供query参数，系统将使用默认问题"请描述这张图片"
4. 上传的图片会临时保存处理后自动删除

### 3.3 久安大模型视频分析接口

#### 接口描述

使用久安大模型分析上传视频的内容。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/jiuan_analyze_video/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名   | 数据类型   | 是否必须 | 备注                                              |
| ----- | ------- | ------ | ---- | ----------------------------------------------- |
| 视频文件  | video   | File   | Y    | 支持的视频格式: .mp4, .avi, .mov, .wmv, .flv, .mkv等    |
| 查询内容  | query   | String | N    | 关于视频的问题，如不提供则默认为"请分析这段视频"                       |
| 历史记录  | history | String | N    | JSON格式的对话历史记录列表（当前WebSocket模式下此参数暂未使用，保留以备后续扩展） |

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                   |
| ----- | ---------- | ------ | ---- | -------------------- |
| 会话ID  | session-id | String | N    | 用于维护会话上下文，如不提供则使用默认值 |

#### 返回参数（非流式输出）

| 返回值显示名 | 返回值物理名  | 返回值类型  | 示例                       |
| ------ | ------- | ------ | ------------------------ |
| 状态     | status  | String | success                  |
| 数据     | data    | Object | 包含久安大模型API的原始响应数据        |
| 错误消息   | message | String | 仅在status为error时返回，描述错误信息 |

久安大模型WebSocket视频分析响应数据格式：

| 返回值显示名 | 返回值物理名 | 返回值类型   | 示例                  |
| ------ | ------ | ------- | ------------------- |
| 分析结果   | result | Object  | 包含describe等字段的分析结果  |
| 状态码    | status | Integer | 200                 |
| 时间戳    | time   | String  | 2025-05-28 14:32:15 |

result分析结果对象包含：

| 返回值显示名 | 返回值物理名   | 返回值类型  | 示例                               |
| ------ | -------- | ------ | -------------------------------- |
| 描述内容   | describe | String | 这段视频展示了消防员在高层建筑火灾救援中的专业操作流程...   |
| 任务ID   | taskId   | String | 59e4905d862849e2b101dbd4035eaafc |
| 分析耗时   | duration | Float  | 15.23                            |
| 分析方式   | method   | String | WebSocket                        |

#### 返回示例

**成功响应示例：**

```json
{
  "status": "success",
  "data": {
    "result": {
      "describe": "这段视频展示了消防员在高层建筑火灾救援中的专业操作流程。视频中可以看到消防员穿着专业防护服，使用云梯车进行高空救援作业。整个过程体现了消防救援的专业性和安全性，包括现场评估、设备部署、人员疏散等关键环节。消防员动作娴熟，配合默契，充分展现了应急救援队伍的训练有素和实战能力。",
      "taskId": "59e4905d862849e2b101dbd4035eaafc",
      "duration": 15.23,
      "method": "WebSocket"
    },
    "status": 200,
    "time": "2025-05-28 14:32:15"
  }
}
```

**错误响应示例：**

```json
{
  "status": "error",
  "message": "请上传有效的视频文件"
}
```

**WebSocket连接超时响应示例：**

```json
{
  "status": "error", 
  "message": "WebSocket连接超时，请检查网络连接和服务状态"
}
```

#### 支持的视频格式

- MP4文件 (.mp4)
- AVI文件 (.avi)  
- MOV文件 (.mov)
- WMV文件 (.wmv)
- FLV文件 (.flv)
- MKV文件 (.mkv)

#### 注意事项

1. 视频文件大小不能超过10MB，超过此限制将被拒绝上传
2. WebSocket连接具有180秒超时机制，超时后会自动重试
3. 如果不提供query参数，系统将使用默认问题"请分析这段视频"
4. 上传的视频会临时保存处理后自动删除

## 4 语音识别与语音交互

### 4.1 语音转文字接口

#### 接口描述

将上传的音频文件转换为文字文本，支持多种音频格式和多种语言识别。

#### 接口版本

v1.0

#### 接口地址

http://{host}:{port}/speech_to_text/

#### 请求方法

POST

#### 请求参数

| 参数显示名 | 参数物理名    | 数据类型   | 是否必须 | 备注                                              |
| ----- | -------- | ------ | ---- | ----------------------------------------------- |
| 音频文件  | audio    | File   | Y    | 支持的音频格式: .wav, .mp3, .m4a, .flac, .ogg, .webm 等 |
| 识别语言  | language | String | N    | 识别语言代码，如"zh"(中文)、"en"(英文)、"auto"(自动检测)，默认为"zh"  |

#### 请求头参数

| 参数显示名 | 参数物理名      | 数据类型   | 是否必须 | 备注                   |
| ----- | ---------- | ------ | ---- | -------------------- |
| 会话ID  | session-id | String | N    | 用于维护会话上下文，如不提供则使用默认值 |

#### 返回参数

| 返回值显示名 | 返回值物理名     | 返回值类型  | 示例                       |
| ------ | ---------- | ------ | ------------------------ |
| 状态     | status     | String | success                  |
| 识别文本   | text       | String | 这是一段关于消防安全检查的录音内容        |
| 识别语言   | language   | String | zh                       |
| 分段信息   | segments   | Array  | 包含音频分段识别结果的数组            |
| 置信度    | confidence | Float  | 0.95                     |
| 错误消息   | message    | String | 仅在status为error时返回，描述错误信息 |

#### segments数组中每个元素包含

| 返回值显示名 | 返回值物理名      | 返回值类型   | 示例       |
| ------ | ----------- | ------- | -------- |
| 段ID    | id          | Integer | 0        |
| 开始时间   | start       | Float   | 0.0      |
| 结束时间   | end         | Float   | 3.5      |
| 分段文本   | text        | String  | 这是第一段内容。 |
| 平均对数概率 | avg_logprob | Float   | -0.2     |

#### 返回示例

**成功响应示例：**

```json
{
  "status": "success",
  "text": "各位领导、同志们，现在开始进行消防安全检查。请确认所有消防通道保持畅通，消防设备处于正常状态。",
  "language": "zh",
  "segments": [
    {
      "id": 0,
      "start": 0.0,
      "end": 3.5,
      "text": "各位领导、同志们，现在开始进行消防安全检查。",
      "avg_logprob": -0.15
    },
    {
      "id": 1,
      "start": 3.5,
      "end": 7.2,
      "text": "请确认所有消防通道保持畅通，消防设备处于正常状态。",
      "avg_logprob": -0.12
    }
  ],
  "confidence": 0.92
}
```

**错误响应示例：**

```json
{
  "status": "error",
  "message": "不支持的音频格式。支持格式：WAV, MP3, M4A, FLAC, OGG, WEBM"
}
```

```json
{
  "status": "error",
  "message": "文件大小超过10MB限制"
}
```

```json
{
  "status": "error",
  "message": "语音识别服务未启用"
}
```

#### 支持的音频格式

- WAV文件 (.wav)
- MP3文件 (.mp3)
- M4A文件 (.m4a)
- FLAC文件 (.flac)
- OGG文件 (.ogg)
- WebM文件 (.webm)

#### 支持的语言

| 语言代码 | 语言名称 |
| ---- | ---- |
| zh   | 中文   |
| en   | 英文   |
| auto | 自动检测 |

#### 注意事项

1. 音频文件大小不能超过10MB
2. 系统支持自动音频格式转换，但建议使用标准格式以获得最佳性能
3. WebM格式会尝试多种转换方法，可能需要额外的依赖库支持
4. 上传的音频文件会被临时保存处理后自动删除
5. 语音识别基于Whisper模型，支持多种语言和方言
6. 置信度范围为0.0-1.0，数值越高表示识别准确度越高
7. 如果语音识别服务未启用，接口将返回相应错误信息
