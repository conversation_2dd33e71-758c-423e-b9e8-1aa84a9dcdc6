import httpx
import json
import base64
import os
import logging
import time
import asyncio
import uuid
import websockets
import urllib.parse

# 导入函数调用模块
try:
    from src.jiuan_agent import function_calling_agent
    ENABLE_FUNCTION_CALLING = True
except ImportError:
    ENABLE_FUNCTION_CALLING = False
    logging.warning("久安大模型函数调用模块导入失败，函数调用功能已禁用")

# 全局变量存储access_token和过期时间
_access_token = None
_token_expires_at = 0

async def get_access_token(config, logger):
    """
    获取访问令牌

    Args:
        config: 配置对象
        logger: 日志对象

    Returns:
        str: 访问令牌，如果获取失败返回None
    """
    global _access_token, _token_expires_at

    try:
        # 检查当前token是否还有效（提前5分钟刷新）
        current_time = time.time()
        if _access_token and current_time < (_token_expires_at - 300):
            logger.info("使用缓存的access_token")
            return _access_token

        # 获取配置信息
        base_url = config.get("JIUAN_API", "BASE_URL")
        token_path = config.get("JIUAN_API", "TOKEN_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")
        client_secret = config.get("JIUAN_API", "CLIENT_SECRET")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        # 构建token获取URL
        token_url = f"{base_url}{token_path}?client_id={client_id}&client_secret={client_secret}"

        logger.info(f"准备获取access_token: {base_url}{token_path}")

        # 发送请求获取token
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            response = await client.get(token_url, timeout=30)

            if response.status_code != 200:
                logger.error(f"获取access_token失败: {response.status_code}, {response.text}")
                return None

            token_data = response.json()
            logger.info(f"Token响应: {json.dumps(token_data, ensure_ascii=False)[:100]}...")

            # 提取access_token
            access_token = token_data.get("access_token")
            expires_in = token_data.get("expires_in", 3600)  # 默认1小时

            if not access_token:
                logger.error("响应中未找到access_token")
                return None

            # 更新全局变量
            _access_token = access_token
            _token_expires_at = current_time + expires_in

            logger.info(f"成功获取access_token，有效期: {expires_in}秒")
            return access_token

    except Exception as e:
        logger.error(f"获取access_token时出错: {str(e)}")
        return None

async def chat_with_model(chat_data, config, logger):
    """
    与大模型进行对话

    Args:
        chat_data: 聊天数据，包含完整的API请求格式
        config: 配置对象
        logger: 日志对象

    Returns:
        dict: 大模型的响应结果
    """
    try:
        # 从chat_data中提取用户消息用于函数调用检查
        user_message = ""
        
        if isinstance(chat_data, dict) and "object" in chat_data:
            messages = chat_data.get("object", {}).get("messages", [])
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break
        elif isinstance(chat_data, str):
            # 兼容旧格式
            user_message = chat_data
        elif isinstance(chat_data, dict) and "query" in chat_data:
            # 直接使用新格式
            user_message = chat_data.get("query", "")

        if not user_message:
            return {
                "status": "error",
                "message": "用户消息不能为空"
            }

        # 检查是否是API函数调用请求
        if ENABLE_FUNCTION_CALLING and user_message:
            # 使用LangChain Agent处理查询
            logger.info(f"尝试使用Agent处理查询: {user_message}")
            function_result = await function_calling_agent(user_message)
            if function_result:
                # 如果有结果，直接返回
                logger.info(f"LangChain Agent处理成功，返回结果")
                return {
                    "status": "success",
                    "data": {
                        "result": function_result,
                        "is_function_call": True
                    }
                }
            else:
                logger.info(f"LangChain Agent未返回结果，切换到常规对话模式")
        else:
            logger.info("函数调用功能未启用或无用户消息，使用常规对话模式")

        # 获取access_token
        access_token = await get_oauth_token(config, logger)
        if not access_token:
            return {
                "status": "error",
                "message": "无法获取访问令牌，请检查配置"
            }

        # 获取有效的会话ID
        conversation_id = await get_conversation_id(access_token, config, logger)
        if not conversation_id:
            return {
                "status": "error",
                "message": "无法获取会话ID，请检查配置"
            }

        # 获取配置信息 - 从JIUAN_API部分获取
        base_url = config.get("JIUAN_API", "BASE_URL")
        chat_path = config.get("JIUAN_API", "CHAT_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")

        # 组合成完整URL，包含client_id和access_token参数
        url = f"{base_url}{chat_path}?client_id={client_id}&access_token={access_token}"
        content_type = config.get("JIUAN_API", "CHAT_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("JIUAN_API", "CHAT_TIMEOUT", fallback=120)
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        logger.info(f"准备调用久安大模型对话接口: {base_url}{chat_path}")
        logger.info(f"用户消息: {user_message}")
        logger.info(f"使用会话ID: {conversation_id}")

        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Accept": "application/json"
            #"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        # 根据API文档要求构造请求体
        payload = {
            "query": user_message,
            "stream": False,
            "conversation_id": conversation_id
        }
        
        # 如果有app_id配置，添加到请求体中
        app_id = config.get("JIUAN_API", "APP_ID", fallback=None)
        if app_id and app_id.strip():
            payload["app_id"] = app_id

        logger.info(f"请求体: {json.dumps(payload, ensure_ascii=False)}")
        logger.info(f"请求URL: {url}")
        logger.info(f"请求头: {headers}")

        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到久安大模型对话接口")
            response = await client.post(
                url,
                json=payload,
                headers=headers,
                timeout=timeout
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"对话接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }

            # 解析响应内容
            response_data = response.json()
            logger.info(f"对话接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")

            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }

    except Exception as e:
        logger.error(f"调用对话接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用对话接口时出错: {str(e)}"
        }

async def get_oauth_token(config, logger):
    """
    获取OAuth访问令牌

    Args:
        config: 配置对象
        logger: 日志对象

    Returns:
        str: 访问令牌，如果获取失败返回None
    """
    try:
        # 获取OAuth配置
        base_url = config.get("JIUAN_API", "BASE_URL")
        token_path = config.get("JIUAN_API", "TOKEN_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")
        client_secret = config.get("JIUAN_API", "CLIENT_SECRET")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        # 组合完整的token获取URL
        token_url = f"{base_url}{token_path}"

        logger.info(f"准备获取OAuth token: {token_url}")

        # 准备请求参数
        params = {
            "client_id": client_id,
            "client_secret": client_secret
        }

        # 发送请求获取token
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            response = await client.get(token_url, params=params, timeout=30)

            if response.status_code != 200:
                logger.error(f"获取OAuth token失败: {response.status_code}, {response.text}")
                return None

            # 解析响应
            token_data = response.json()
            access_token = token_data.get("access_token")

            if access_token:
                logger.info("OAuth token获取成功")
                return access_token
            else:
                logger.error(f"响应中未找到access_token: {token_data}")
                return None

    except Exception as e:
        logger.error(f"获取OAuth token时出错: {str(e)}")
        return None

async def analyze_image(file_path, query, config, logger, history=None):
    """
    分析图片内容

    Args:
        file_path: 图片文件路径
        query: 用户查询内容
        config: 配置对象
        logger: 日志对象
        history: 对话历史记录，默认为空列表

    Returns:
        dict: 图片分析结果
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"图片文件不存在: {file_path}")
            return {"status": "error", "message": "图片文件不存在"}

        # 获取OAuth token
        access_token = await get_oauth_token(config, logger)
        if not access_token:
            return {"status": "error", "message": "获取访问令牌失败"}

        # 获取配置信息 - 从JIUAN_API部分获取
        base_url = config.get("JIUAN_API", "BASE_URL")
        image_path = config.get("JIUAN_API", "IMAGE_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")
        # 组合成完整URL
        url = f"{base_url}{image_path}"
        content_type = config.get("JIUAN_API", "IMAGE_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("JIUAN_API", "IMAGE_TIMEOUT", fallback=120)
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        logger.info(f"准备调用图片分析接口: {url}")
        logger.info(f"图片文件: {file_path}, 查询: {query}")

        # 读取图片文件并转换为base64
        with open(file_path, "rb") as image_file:
            image_content = image_file.read()
            image_base64 = base64.b64encode(image_content).decode('utf-8')

        logger.info(f"图片转换为base64成功，长度: {len(image_base64)}")

        # 准备请求头
        headers = {
            "Content-Type": content_type
        }

        # 如果没有提供history，使用空列表
        if history is None:
            history = []

        # 准备请求体 - 根据API文档要求的字段格式
        payload = {
            "text": query,
            "history": history,
            "image": image_base64
        }

        # 准备URL参数 - 根据API文档要求添加client_id和access_token
        params = {
            "client_id": client_id,
            "access_token": access_token
        }

        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到图片分析接口")
            response = await client.post(
                url,
                json=payload,
                headers=headers,
                params=params,
                timeout=timeout
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"图片分析接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }

            # 解析响应内容
            response_data = response.json()
            logger.info(f"图片分析接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")

            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }

    except Exception as e:
        logger.error(f"调用图片分析接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用图片分析接口时出错: {str(e)}"
        }

async def emergency_knowledge_qa(user_message, config, logger):
    """
    应急行业知识问答

    Args:
        user_message: 用户输入的消息
        config: 配置对象
        logger: 日志对象

    Returns:
        dict: 应急知识问答的响应结果
    """
    try:
        # 检查是否是API函数调用请求
        if ENABLE_FUNCTION_CALLING:
            # 使用LangChain Agent处理查询
            logger.info(f"尝试使用LangChain Agent处理应急知识查询: {user_message}")
            function_result = await function_calling_agent(user_message)
            if function_result:
                # 如果有结果，直接返回
                logger.info(f"LangChain Agent处理成功，返回结果")
                return {
                    "status": "success",
                    "data": {
                        "result": function_result,
                        "is_function_call": True
                    }
                }
            else:
                logger.info(f"LangChain Agent未返回结果，切换到应急知识问答模式")
        else:
            logger.info("函数调用功能未启用，使用应急知识问答模式")

        # 获取OAuth token
        access_token = await get_oauth_token(config, logger)
        if not access_token:
            return {"status": "error", "message": "获取访问令牌失败"}

        # 获取配置信息 - 从JIUAN_API部分获取
        base_url = config.get("JIUAN_API", "BASE_URL")
        emergency_path = config.get("JIUAN_API", "EMERGENCY_KNOWLEDGE_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")

        # 处理路径中的{x}占位符，这里需要根据具体需求替换
        # 根据API文档，{x}可能是插件ID或其他标识符，这里暂时使用默认值
        emergency_path = emergency_path.replace("{x}", "emergency_qa")

        # 组合成完整URL，包含client_id和access_token参数
        url = f"{base_url}{emergency_path}?client_id={client_id}&access_token={access_token}"
        content_type = config.get("JIUAN_API", "EMERGENCY_KNOWLEDGE_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("JIUAN_API", "EMERGENCY_KNOWLEDGE_TIMEOUT", fallback=120)
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        logger.info(f"准备调用久安大模型应急知识问答接口: {base_url}{emergency_path}")
        logger.info(f"用户消息: {user_message}")

        # 准备请求头
        headers = {
            "Content-Type": content_type
        }

        # 准备请求体 - 根据API文档要求的格式
        payload = {
            "question": user_message,
            "domain": "emergency",  # 指定应急行业领域
            "model": "emergency-knowledge-qa",
            "context": {
                "industry": "emergency_management",
                "type": "knowledge_qa"
            }
        }

        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到久安大模型应急知识问答接口")
            response = await client.post(
                url,
                json=payload,
                headers=headers,
                timeout=timeout
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"应急知识问答接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }

            # 解析响应内容
            response_data = response.json()
            logger.info(f"应急知识问答接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")

            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }

    except Exception as e:
        logger.error(f"调用应急知识问答接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用应急知识问答接口时出错: {str(e)}"
        }

async def get_conversation_id(access_token, config, logger):
    """
    获取久安大模型的会话ID

    Args:
        access_token: 已获取的访问令牌
        config: 配置对象
        logger: 日志对象

    Returns:
        str: 会话ID，如果获取失败返回None
    """
    try:
        # 获取配置信息
        base_url = config.get("JIUAN_API", "BASE_URL")
        conversation_path = config.get("JIUAN_API", "CONVERSATION_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        # 构建获取会话ID的URL
        conversation_url = f"{base_url}{conversation_path}?client_id={client_id}&access_token={access_token}"

        logger.info(f"准备获取会话ID: {conversation_url}")

        # 准备请求头
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
            #"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        # 尝试不同的请求方式
        # 方式1：发送空的JSON请求体
        payload = {}

        logger.info(f"发送POST请求获取会话ID，URL: {conversation_url}")
        logger.info(f"请求头: {headers}")
        logger.info(f"请求体: {payload}")

        # 发送请求获取会话ID
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            response = await client.post(
                conversation_url, 
                json=payload,
                headers=headers,
                timeout=30
            )

            if response.status_code != 200:
                logger.error(f"获取会话ID失败: {response.status_code}, {response.text}")
                return None

            # 解析响应
            conversation_data = response.json()
            logger.info(f"会话ID响应: {json.dumps(conversation_data, ensure_ascii=False)[:100]}...")

            # 提取会话ID（根据API响应格式调整）
            conversation_id = conversation_data.get("conversation_id") or conversation_data.get("id") or conversation_data.get("data")

            if conversation_id:
                logger.info(f"成功获取会话ID: {conversation_id}")
                return conversation_id
            else:
                logger.error(f"响应中未找到会话ID: {conversation_data}")
                return None

    except Exception as e:
        logger.error(f"获取会话ID时出错: {str(e)}")
        return None

async def get_websocket_video_analysis(file_path, question, config, logger):
    """
    使用WebSocket进行视频分析的完整流程
    
    Args:
        file_path: 视频文件路径
        question: 分析问题
        config: 配置对象
        logger: 日志对象
        
    Returns:
        dict: 视频分析结果
    """
    try:
        # 第一步：获取access_token
        access_token = await get_websocket_oauth_token(config, logger)
        if not access_token:
            return {"status": "error", "message": "获取访问令牌失败"}
        
        # 第二步：建立WebSocket连接并获取taskId，然后保持连接等待结果
        result = await websocket_analysis_with_persistent_connection(file_path, question, access_token, config, logger)
        
        return result
        
    except Exception as e:
        logger.error(f"WebSocket视频分析流程出错: {str(e)}")
        return {
            "status": "error",
            "message": f"视频分析流程出错: {str(e)}"
        }

async def websocket_analysis_with_persistent_connection(file_path, question, access_token, config, logger, max_wait_time=600):
    """
    使用持久WebSocket连接进行视频分析
    
    Args:
        file_path: 视频文件路径
        question: 分析问题
        access_token: 访问令牌
        config: 配置对象
        logger: 日志对象
        max_wait_time: 最大等待时间（秒）
        
    Returns:
        dict: 分析结果
    """
    try:
        # 获取配置信息 - 使用JIUAN_API配置
        base_url = config.get("JIUAN_API", "BASE_URL")
        client_id = config.get("JIUAN_API", "CLIENT_ID")
        video_result_path = config.get("JIUAN_API", "VIDEO_RESULT_PATH")
        
        # 构建WebSocket URL - 将http://替换为ws://
        ws_base_url = base_url.replace("http://", "ws://").replace("https://", "wss://")
        ws_url = f"{ws_base_url}{video_result_path}?client_id={client_id}&access_token={access_token}"
        
        logger.info(f"准备建立持久WebSocket连接: {ws_url}")
        
        # 建立WebSocket连接并保持打开状态
        async with websockets.connect(ws_url) as websocket:
            logger.info("WebSocket连接已建立，等待taskId...")
            
            # 第一步：等待服务端返回taskId
            response = await websocket.recv()
            logger.info(f"收到WebSocket响应: {response}")
            
            # Base64解码taskId
            try:
                decoded_response = base64.b64decode(response).decode('utf-8')
                logger.info(f"解码后的响应: {decoded_response}")
                
                # 解析JSON获取taskId
                task_data = json.loads(decoded_response)
                task_id = task_data.get("taskId")
                
                if not task_id:
                    logger.error(f"响应中未找到taskId: {task_data}")
                    return {"status": "error", "message": "获取taskId失败"}
                    
                logger.info(f"成功获取taskId: {task_id}")
                
            except Exception as decode_error:
                logger.error(f"解码taskId失败: {str(decode_error)}")
                return {"status": "error", "message": "解码taskId失败"}
            
            # 第二步：上传视频文件
            upload_result = await upload_video_via_http(file_path, question, task_id, access_token, config, logger)
            if not upload_result:
                return {"status": "error", "message": "视频上传失败"}
            
            # 第三步：在同一个WebSocket连接中等待分析结果
            logger.info(f"准备在持久连接中等待分析结果，任务ID: {task_id}")
            start_time = time.time()
            
            while True:
                current_time = time.time()
                if current_time - start_time > max_wait_time:
                    logger.warning(f"等待分析结果超时，任务ID: {task_id}")
                    return {"status": "timeout", "message": "等待分析结果超时", "task_id": task_id}
                
                try:
                    # 设置接收超时
                    response = await asyncio.wait_for(websocket.recv(), timeout=30)
                    logger.info(f"收到WebSocket响应: {response[:100]}...")
                    
                    # Base64解码响应
                    try:
                        decoded_response = base64.b64decode(response).decode('utf-8')
                        logger.info(f"解码后的响应: {decoded_response[:200]}...")
                        
                        # 解析JSON响应
                        result_data = json.loads(decoded_response)
                        
                        # 检查是否是我们的任务结果
                        response_task_id = result_data.get("taskId") or result_data.get("task_id")
                        if response_task_id == task_id:
                            logger.info(f"收到匹配的任务结果，任务ID: {task_id}")
                            
                            # 构造符合期望格式的返回结果
                            from datetime import datetime
                            return {
                                "status": "success",
                                "data": {
                                    "result": {
                                        "describe": result_data.get("describe") or result_data.get("msg") or result_data.get("response") or str(result_data),
                                        "taskId": task_id,
                                        "duration": round(current_time - start_time, 2),
                                        "method": "WebSocket"
                                    },
                                    "status": 200,
                                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                }
                            }
                        else:
                            logger.info(f"收到其他任务的结果，继续等待。收到任务ID: {response_task_id}, 期望任务ID: {task_id}")
                            
                    except Exception as decode_error:
                        logger.warning(f"解码WebSocket响应失败: {str(decode_error)}")
                        continue
                        
                except asyncio.TimeoutError:
                    logger.info(f"等待WebSocket响应超时，继续等待...")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    logger.warning("WebSocket连接已关闭")
                    return {"status": "error", "message": "WebSocket连接异常关闭"}
                    
    except Exception as e:
        logger.error(f"持久WebSocket连接分析时出错: {str(e)}")
        return {"status": "error", "message": f"WebSocket分析时出错: {str(e)}"}

async def get_websocket_oauth_token(config, logger):
    """
    获取WebSocket视频分析的OAuth访问令牌
    
    Args:
        config: 配置对象
        logger: 日志对象
        
    Returns:
        str: 访问令牌，如果获取失败返回None
    """
    try:
        # 获取配置信息 - 使用JIUAN_API配置
        base_url = config.get("JIUAN_API", "BASE_URL")
        token_path = config.get("JIUAN_API", "TOKEN_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")
        client_secret = config.get("JIUAN_API", "CLIENT_SECRET")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)
        
        # 构建token获取URL
        token_url = f"{base_url}{token_path}?client_id={client_id}&client_secret={client_secret}"
        
        logger.info(f"准备获取WebSocket视频分析access_token: {token_url}")
        
        # 发送请求获取token
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            response = await client.get(token_url, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"获取WebSocket视频分析access_token失败: {response.status_code}, {response.text}")
                return None
            
            token_data = response.json()
            logger.info(f"WebSocket视频分析Token响应: {json.dumps(token_data, ensure_ascii=False)}")
            
            access_token = token_data.get("access_token")
            if access_token:
                logger.info("WebSocket视频分析access_token获取成功")
                return access_token
            else:
                logger.error(f"响应中未找到access_token: {token_data}")
                return None
                
    except Exception as e:
        logger.error(f"获取WebSocket视频分析access_token时出错: {str(e)}")
        return None

async def upload_video_via_http(file_path, question, task_id, access_token, config, logger):
    """
    通过HTTP接口上传视频文件
    
    Args:
        file_path: 视频文件路径
        question: 分析问题
        task_id: 任务ID
        access_token: 访问令牌
        config: 配置对象
        logger: 日志对象
        
    Returns:
        bool: 上传是否成功
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"视频文件不存在: {file_path}")
            return False
        
        # 获取配置信息 - 使用JIUAN_API配置
        base_url = config.get("JIUAN_API", "BASE_URL")
        video_path = config.get("JIUAN_API", "VIDEO_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)
        
        # 获取文件名
        filename = os.path.basename(file_path)
        
        # URL编码问题参数
        encoded_question = urllib.parse.quote(question)
        
        # 构建上传URL
        upload_url = f"{base_url}{video_path}?question={encoded_question}&taskId={task_id}&filename={filename}&client_id={client_id}&access_token={access_token}"
        
        logger.info(f"准备上传视频文件: {upload_url}")
        logger.info(f"视频文件: {file_path}, 问题: {question}, 任务ID: {task_id}")
        
        # 读取视频文件
        with open(file_path, "rb") as video_file:
            video_content = video_file.read()
        
        logger.info(f"视频文件大小: {len(video_content)} 字节")
        
        # 上传视频文件
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            response = await client.post(
                upload_url,
                content=video_content,
                headers={"Content-Type": "application/octet-stream"},
                timeout=300  # 5分钟超时
            )
            
            if response.status_code == 200:
                logger.info("视频文件上传成功")
                logger.info(f"上传响应: {response.text}")
                return True
            else:
                logger.error(f"视频文件上传失败: {response.status_code}, {response.text}")
                return False
                
    except Exception as e:
        logger.error(f"上传视频文件时出错: {str(e)}")
        return False
