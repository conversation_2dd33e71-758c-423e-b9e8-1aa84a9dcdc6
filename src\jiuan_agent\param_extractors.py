"""
久安大模型参数提取模块 - 包含从用户输入提取API参数的相关函数
"""

import re
import logging
from typing import Dict, Any

# 创建logger
logger = logging.getLogger("jiuan_agent")

def extract_event_params(user_input: str) -> Dict[str, Any]:
    """
    从用户输入中提取事件名称和距离参数
    """
    event_name = ""
    distance = 0
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请你告诉我", "请告诉我", "请查询", "查询", "请问", "告诉我", 
               "帮我查一下", "帮我看看", "帮忙查询", "帮忙看看", "麻烦查询", 
               "麻烦告诉我", "我想知道", "能告诉我", "能查询", "能帮我查询",
               "想了解", "查一下", "看一下", "帮我问一下", "帮我确认一下",
               "请帮我查看", "请帮我确认", "请问一下", "麻烦问一下", 
               "麻烦帮我查", "能否告诉我", "能否查询", "能否帮我看", 
               "想请教", "请协助查询", "协助查看", "帮忙问一下", "我想知道", "查找"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 各种匹配模式
    patterns = [
        # 格式1：XX事件[X公里]范围内
        r'([\w\s]+?事件)(?:[^\d]*?)(\d+)(?:公里|km|千米)(?:(?:范围|半径)内|以内|附近)',
        # 格式2：XX事件附近X公里
        r'([\w\s]+?事件)(?:附近|周边|周围)(?:[^\d]*?)(\d+)(?:公里|km|千米)',
        # 格式3：XX事件的X公里范围
        r'([\w\s]+?事件)的(?:[^\d]*?)(\d+)(?:公里|km|千米)(?:范围|半径|内|附近|周边)',
        # 格式4：XX事件（不含距离）
        r'([\w\s]+?事件)(?=\s|$|，|,|的)'
    ]
    
    # 依次尝试各种模式
    for pattern in patterns:
        matches = re.search(pattern, cleaned_input)
        if matches and len(matches.groups()) >= 1:
            if not event_name:  # 如果之前没提取到，则提取
                event_name = matches.group(1).strip()
            if len(matches.groups()) >= 2 and matches.group(2):  # 如果包含距离信息
                try:
                    distance = int(matches.group(2))
                except ValueError:
                    pass
            break  # 找到一种匹配就退出循环
    
    # 清理提取到的事件名称中的标点符号
    if event_name:
        # 移除常见中英文标点符号
        event_name = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', event_name).strip()
        
    # 清理提取出的事件名称，移除"发生了"等前缀
    if event_name:
        # 移除"发生了"、"发生"、"爆发了"等前缀
        event_name = re.sub(r'^(发生了|发生|爆发了|爆发|出现了|出现|有了|有|最近的)', '', event_name).strip()
    
    # 如果没有提取到距离，设置默认值
    if distance <= 0:
        distance = 5  # 默认5公里
    
    # 记录提取结果
    logger.info(f"提取到事件参数 - 事件名称: {event_name}, 距离: {distance}")
    
    return {
        "event_name": event_name,
        "distance": distance
    }

def extract_camera_params(user_input: str) -> dict:
    """
    从用户输入中提取摄像头ID参数
    """
    camera_id = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请你", "请", "帮我", "我想", "我要", "我希望", "能否", "能不能", 
                "是否可以", "麻烦", "麻烦你", "帮忙", "想要", "需要", "我需要", "希望能"]
    for prefix in prefixes:
        if cleaned_input.lower().startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):].strip()
            break
        
    # 各种匹配模式，使用命名捕获组提高可读性
    patterns = [
        # 特殊格式：数字-Camera 数字
        r'(?i)(?P<id>\d+\s*-\s*[Cc]amera\s+\d+)',
        
        # 常见操作+摄像头格式
        r'(?:预览|查看|打开|开启)(?:一下|下)?(?:摄像头|监控|摄像机|监控点|点位|探头|[Cc]amera)?(?:画面)?[:：\s]*(?P<id>[a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',
        
        # 摄像头+ID格式
        r'(?:摄像头|监控|摄像机|监控点|点位|探头)(?:[:：\s]*)(?P<id>[a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',
        
        # ID+摄像头格式
        r'(?P<id>[a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)(?:的)?(?:摄像头|监控|摄像机|点位|探头)(?:的)?(?:画面|内容|视频|录像|预览|监控)?',
        
        # Camera+ID格式
        r'[Cc]amera\s*(?P<id>[a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',
    ]
    
    # 遍历所有模式尝试匹配
    for pattern in patterns:
        match = re.search(pattern, cleaned_input)
        if match and 'id' in match.groupdict():
            camera_id = match.group('id').strip()
            break
    
    # 清理提取到的摄像头ID中的标点符号，但保留连字符"-"
    if camera_id:
        camera_id = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', camera_id).strip()
    
    return {
        "camera_id": camera_id
    }

# 完善资源参数提取函数
def extract_resource_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取资源名称参数"""
    resource_name = ""
    resource_type = "default"  # 默认类型
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 各种匹配模式
    resource_patterns = [
        r'一张图查看(?:并定位)?\s*(.+?)(?:$|\s|，|。)',  # 匹配"一张图查看XX"
        r'一张图定位\s*(.+?)(?:$|\s|，|。)',  # 匹配"一张图定位XX"
        r'定位\s*(.+?)(?:$|\s|，|。)',  # 匹配"定位XX"
        r'查看\s*(.+?)的位置',  # 匹配"查看XX的位置"
        r'查询\s*(.+?)的位置',  # 匹配"查询XX的位置"
        r'(?:查询|查看)\s*(.+?)(?:位置|在哪里|在哪儿|在什么地方)',  # 匹配"查询XX位置"
    ]
    
    # 遍历所有模式尝试匹配
    for pattern in resource_patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            resource_name = match.group(1).strip()
            break
    
    # 清理提取到的资源名称中的标点符号
    if resource_name:
        resource_name = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', resource_name).strip()
    
    logger.info(f"提取到资源参数 - 资源名称: {resource_name}, 资源类型: {resource_type}")
    
    return {
        "resource_name": resource_name,
        "resource_type": resource_type
    }

def extract_plan_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取预案类型"""
    plan_type = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 如果用户明确要查询所有预案
    if "所有预案" in cleaned_input or "全部预案" in cleaned_input:
        return {"plan_type": ""}
    
    # 各种匹配模式
    patterns = [
        r'搜索(?:一下|下)?(?P<type>[\w\d]+?)(?:类型|类)?(?:的)?(?:应急)?预案',
        r'查找(?:一下|下)?(?P<type>[\w\d]+?)(?:类型|类)?(?:的)?(?:应急)?预案',
        r'查询(?:一下|下)?(?P<type>[\w\d]+?)(?:类型|类)?(?:的)?(?:应急)?预案',
        r'(?P<type>[\w\d]+?)(?:类型|类)?(?:的)?(?:应急)?预案(?:查询|搜索|列表)',
        r'(?P<type>[\w\d]+?)(?:类型|类|方面|相关|领域)(?:的)?(?:应急)?预案'
    ]
    
    # 依次尝试各种模式
    for pattern in patterns:
        matches = re.search(pattern, cleaned_input)
        if matches:
            plan_type = matches.group('type').strip()
            break
    
    # 如果没有找到具体类型但包含预案查询的意图
    if not plan_type and any(keyword in cleaned_input for keyword in ["查询预案", "搜索预案", "查找预案", "预案查询"]):
        plan_type = ""
    
    logger.info(f"提取到预案参数 - 预案类型: {plan_type}")
    
    return {
        "plan_type": plan_type
    }

def extract_event_query_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取事件查询参数"""
    page = 1
    page_size = 50
    event_status = ""
    event_name = ""
    event_level = ""
    
    # 提取页码信息
    page_match = re.search(r'第(\d+)页', user_input)
    if page_match:
        page = int(page_match.group(1))
    
    # 提取页面大小
    size_match = re.search(r'(\d+)条', user_input)
    if size_match:
        page_size = int(size_match.group(1))
    
    # 提取事件名称
    name_patterns = [
        r'名称包含(.+?)的事件',
        r'(.+?)事件',
        r'事件名称.*?([^，,\s]+)'
    ]
    
    for pattern in name_patterns:
        match = re.search(pattern, user_input)
        if match:
            event_name = match.group(1).strip()
            break
    
    logger.info(f"提取到事件查询参数 - 页码: {page}, 页面大小: {page_size}, 事件名称: {event_name}")
    
    return {
        "page": page,
        "page_no": page,
        "page_size": page_size,
        "event_status": event_status,
        "event_name": event_name,
        "event_level": event_level
    }

def extract_area_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取区域名称"""
    area_name = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要", "查询", "搜索"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 尝试匹配"XX市"或"XX区"格式
    area_patterns = [
        r'([^,，\s]+?(?:市|区))(?:的)?(?:所有|全部)?(?:应急)?预案',
        r'查询(?:[^,，\s]+?)?([^,，\s]+?(?:市|区))(?:的)?(?:所有|全部)?(?:应急)?预案',
        r'搜索(?:[^,，\s]+?)?([^,，\s]+?(?:市|区))(?:的)?(?:所有|全部)?(?:应急)?预案',
        r'(?:所有|全部)?([^,，\s]+?(?:市|区))(?:的)?(?:应急)?预案',
    ]
    
    # 依次尝试各种模式
    for pattern in area_patterns:
        matches = re.search(pattern, cleaned_input)
        if matches:
            area_name = matches.group(1).strip()
            break
    
    logger.info(f"提取到区域参数 - 区域名称: {area_name}")
    
    return {
        "area_name": area_name
    }

def extract_resource_camera_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取资源周边视频参数"""
    resource_name = ""
    resource_type = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要", 
               "查询", "搜索", "查看", "打开", "显示", "展示"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 资源类型映射（中文到英文）
    resource_type_map = {
        "防护目标": "protection_target",
        "救援队伍": "rescue_team",
        "应急仓库": "repository",
        "物资仓库": "repository",
        "企业": "enterpriseInfo",
        "人员": "person"
    }
    
    # 尝试同时提取资源名称和类型
    for zh_type, en_type in resource_type_map.items():
        pattern = fr'([^,，\s]+?{zh_type})(?:周边|附近|旁边|的|周围)?(?:视频|摄像头|监控|摄像机|监控点|点位)'
        match = re.search(pattern, cleaned_input)
        if match:
            resource_name = match.group(1).replace(zh_type, "").strip()
            resource_type = en_type
            break
    
    # 如果没有匹配到完整资源名称和类型，尝试从"周边"、"附近"等词前面提取资源名称
    if not resource_name:
        surroundings = ["周边", "附近", "旁边", "周围"]
        for surround in surroundings:
            if surround in cleaned_input:
                parts = cleaned_input.split(surround)
                if len(parts) > 1:
                    resource_name = parts[0].strip()
                    # 设置默认类型
                    resource_type = "repository"  # 默认为应急仓库
                    break
    
    logger.info(f"提取到资源周边视频参数 - 资源名称: {resource_name}, 资源类型: {resource_type}")
    
    return {
        "resource_name": resource_name,
        "resource_type": resource_type
    }

def extract_call_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取呼叫参数"""
    resource_name = ""
    resource_type = ""
    calling_type = "0"  # 默认为视频通话
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 确定呼叫类型
    if "视频" in cleaned_input:
        calling_type = "0"
    elif "电话" in cleaned_input:
        calling_type = "1"
    
    # 资源类型映射
    resource_type_keywords = {
        "队伍": "rescue_team",
        "救援队": "rescue_team",
        "救援队伍": "rescue_team",
        "人员": "person",
        "队员": "person",
        "指挥员": "person",
        "负责人": "person"
    }
    
    # 提取资源名称和类型
    for keyword, r_type in resource_type_keywords.items():
        if keyword in cleaned_input:
            resource_type = r_type
            # 提取名称
            pattern = fr'([^,，\s]+?)(?:{keyword})'
            match = re.search(pattern, cleaned_input)
            if match:
                resource_name = match.group(1).strip()
                break
    
    # 如果没有匹配到，尝试提取引号中的内容
    if not resource_name:
        quote_matches = re.findall(r'["\'""'']([^"\'""'']+)["\'""'']', cleaned_input)
        if quote_matches:
            resource_name = quote_matches[0].strip()
            resource_type = "person"  # 默认为人员
    
    logger.info(f"提取到呼叫参数 - 资源名称: {resource_name}, 资源类型: {resource_type}, 呼叫类型: {calling_type}")
    
    return {
        "resource_name": resource_name,
        "resource_type": resource_type,
        "calling_type": calling_type
    }

def extract_model_name(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取模型名称"""
    model_name = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要", "按照", "根据", "使用"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 各种匹配模式
    patterns = [
        r'([^,，\s]+?模型)(?:附近|周边|周围|搜索|查询)',
        r'模型(?:[:：\s]*)?([^,，\s]+)',
        r'按照(?:\s*)?([^,，\s]+?模型)',
        r'根据(?:\s*)?([^,，\s]+?模型)',
        r'使用(?:\s*)?([^,，\s]+?模型)',
    ]
    
    # 依次尝试各种模式
    for pattern in patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            model_name = match.group(1).strip()
            break
    
    logger.info(f"提取到模型参数 - 模型名称: {model_name}")
    
    return {
        "model_name": model_name
    }

def extract_real_time_travel(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取实时轨迹参数"""
    is_real_time_travel = False
    
    # 实时轨迹关键词
    travel_keywords = [
        "启动一键调度", "跟进实时轨迹", "救援队伍轨迹", "实时跟踪", "实时跟进",
        "一键调度", "实时轨迹", "轨迹跟踪", "队伍跟踪", "调度跟进"
    ]
    
    # 检查是否包含实时轨迹相关关键词
    if any(keyword in user_input for keyword in travel_keywords):
        is_real_time_travel = True
    
    logger.info(f"提取到实时轨迹参数 - 是否实时轨迹: {is_real_time_travel}")
    
    return {
        "is_real_time_travel": is_real_time_travel
    }

def extract_meeting_name(user_input: str, is_start: bool = True) -> Dict[str, Any]:
    """从用户输入中提取会议名称"""
    meeting_name = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    action_prefixes = ["开启", "启动", "开始", "创建", "建立", "结束", "终止", "关闭", "停止"]
    
    for prefix in action_prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 各种匹配模式
    patterns = [
        r'(?:开启|启动|开始|创建|建立|结束|终止|关闭|停止)(?:\s*)?([^,，\s]+?会议)',
        r'([^,，\s]+?会议)(?:开启|启动|开始|创建|建立|结束|终止|关闭|停止)',
        r'会议(?:[:：\s]*)?([^,，\s]+)',
        r'([^,，\s]+?)(?:的)?会议'
    ]
    
    # 依次尝试各种模式
    for pattern in patterns:
        match = re.search(pattern, user_input)
        if match:
            meeting_name = match.group(1).strip()
            # 如果提取的名称不包含"会议"，则添加
            if not meeting_name.endswith("会议"):
                meeting_name += "会议"
            break
    
    # 如果没有提取到会议名称，使用默认名称
    if not meeting_name:
        action = "开启" if is_start else "结束"
        meeting_name = f"应急指挥会议"  # 默认会议名称
    
    logger.info(f"提取到会议参数 - 会议名称: {meeting_name}")
    
    return {
        "meeting_name": meeting_name
    }
